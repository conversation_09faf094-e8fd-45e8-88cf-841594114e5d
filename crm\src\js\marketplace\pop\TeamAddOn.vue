<template>
    <PopWin
        :isShow="isShow"
        :width="1000"
        @close="close"
        v-loading="isLoading"
        class="addon-pop"
        :title="$t('teamAddOn.dialogTitle')"
    >
        <template v-slot:content>
            <div class="pop-addon-content">
                <p class="content-title">{{ $t("teamAddOn.subTitle1") }}</p>
                <p class="content-desc" v-html="$t('teamAddOn.desc1')"></p>
                <div class="card-container">
                    <PickCard
                        v-for="(card, index) in realCardList"
                        :key="index + 'card'"
                        :cardData="card"
                        :isChecked="checkedPkg === card.type"
                        @change="choosePkg"
                    />
                </div>
                <div class="payment-info" :class="{ 'no-card-info': noCard }">
                    <PaymentInfo
                        v-if="isCreator"
                        :canAddAnotherCard="!isNewObDashboard"
                        :cardList="creatorCardList"
                        :needBindCard="noCard"
                        @input="newCard = $event"
                        @setCard="setNewCard"
                        @paymentInfoUpdated="calcTax"
                        :validateResult.sync="canPlaceOrder"
                        ref="creatorPay"
                        :bindCardText="bindCardText"
                        :isShowAch="true"
                    />
                    <PaymentInfo
                        v-else
                        :canAddAnotherCard="!isNewObDashboard"
                        :card="memberBankCard"
                        :needBindCard="noCard"
                        @input="newCard = $event"
                        @setCard="setNewCard"
                        @paymentInfoUpdated="calcTax"
                        position="member"
                        ref="memberPay"
                        :validateResult.sync="canPlaceOrder"
                        :bindCardText="bindCardText"
                        :isShowAch="true"
                    />
                </div>
            </div>
        </template>
        <template v-slot:footer>
            <div class="addon-charge-info">
                <ChargeInfo
                    :totalCharge="totalCharge"
                    :showTax="showTax"
                    :defaultTopPadding="false"
                    taxIconStyle="left:30px"
                    :stepNum="1"
                    :step="2"
                    :isRead="isRead"
                    @change="changeIsRead"
                    :chargeText="$t('teamAddOn.chargeDes')"
                    :termsOfUseLinkDesc="$t('teamAddOn.termsOfUseLinkDesc')"
                >
                    <template v-slot:chargeText>
                        <div>
                            <p class="pay-charge">
                                <span class="pay-word">{{ $t("teamAddOn.chargeDes") }}</span>
                                <span class="moeny">{{
                                    $st("common", "price2", {
                                        count: formatToTwoDecimalsLocale(displayedTotalCharge)
                                    })
                                }}</span>
                                <DropDown
                                    v-if="showTax"
                                    class="charge-drop"
                                    :noBorder="true"
                                    :clickMode="true"
                                    placement="top"
                                    dropdownCls="tax-drop-cls"
                                    style="display: inline-block"
                                >
                                    <template #body="data">
                                        <div class="tax-detail-btn flex-row">
                                            <span>{{ $st("common", "chargeInfo.taxDetail") }}</span>
                                            <span
                                                class="tax-detail-btn-icon icon2017 icon-arrow_01_down"
                                                :class="{
                                                    'icon-arrow_01_up': data.isOpen
                                                }"
                                            ></span>
                                        </div>
                                    </template>
                                    <template #dropdown>
                                        <div class="charge-info">
                                            <span class="tip-arrow"></span>
                                            <div class="charge-detail">
                                                <p>
                                                    <span>{{ $st("common", "pay.chargeDes") }}</span
                                                    ><span
                                                        >{{ currencySymbol
                                                        }}{{
                                                            formatToTwoDecimalsLocale(totalCharge)
                                                        }}</span
                                                    >
                                                </p>
                                                <p>
                                                    <span>{{
                                                        $st("common", "chargeInfo.totalTaxes")
                                                    }}</span
                                                    ><span
                                                        >{{ currencySymbol
                                                        }}{{
                                                            formatToTwoDecimalsLocale(totalTax)
                                                        }}</span
                                                    >
                                                </p>
                                            </div>
                                        </div>
                                    </template>
                                </DropDown>
                            </p>
                        </div>
                    </template>
                    <template v-slot:btn>
                        <button class="chime-btn invisible" @click="close">
                            {{ $t("teamAddOn.cancelBtnTxt") }}
                        </button>
                        <div v-tip="{ content: buyTip }">
                            <button
                                type="button"
                                class="chime-btn primary"
                                @click="placeOrder"
                                :class="{
                                    disabledClick: !canPlaceOrder || isLoading
                                }"
                            >
                                {{ $t("teamAddOn.payNow") }}
                            </button>
                        </div>
                    </template>
                </ChargeInfo>
            </div>
        </template>
    </PopWin>
</template>

<script>
import { components, utils } from "common";
import PickCard from "@/js/marketplace/components/PickCard.vue";
import { PaymentInfo, infoData, ChargeInfo, payTipPop, basePermission } from "crm";
import TermOfUsePop from "@/js/common-module/termOfUsePop/index.js";
import useTax from "@/js/common-module/hooks/useTax.js";
import { useCurrencySymbol } from "@/hooks";
import http from "../api.js";

const { PopWin, DropDown } = components;
const teamPackageIdMap = {
    crmonly: 0,
    crmidx: 1,
    singleSeat: 2
};
export default {
    name: "TeamAddOn",
    langModule: "marketPlace",
    components: { PopWin, PickCard, PaymentInfo, ChargeInfo, DropDown },
    setup() {
        const { showTax, taxInfo, openDetailBefore } = useTax();
        const currencySymbol = useCurrencySymbol();
        return { showTax, taxInfo, openDetailBefore, currencySymbol };
    },
    data() {
        const { Ach } = basePermission;
        return {
            Ach,
            isShow: false,
            isLoading: false,
            type: null,
            creatorCardList: [],
            memberBankCard: {},
            canPlaceOrder: false,
            isRead: false,
            totalCharge: "0",
            checkedPkg: "crmonly"
        };
    },
    watch: {
        totalCharge() {
            this.calcTax();
        }
    },
    methods: {
        calcTax() {
            const _ref = this.$refs.creatorPay || this.$refs.memberPay;
            this.openDetailBefore({
                paymentRef: _ref,
                taxItems: [
                    {
                        productServiceId: "PR#TEAM_ADDON",
                        amount: Number(this.totalCharge) || 0,
                        quantity: 1
                    }
                ]
            });
        },
        // Initialize the information in the pop-up window
        async init(opt = {}) {
            this.isShow = true;
            this.isLoading = true;
            this.type = opt.type;
            if (!this.isCreator) {
                let { data } = await http.getMemberCardInfo({
                    searchDataType: this.Ach ? 0 : 1
                });
                this.memberBankCard = data ? data : {};
                //  no card
                // this.memberBankCard = {};
                this.canPlaceOrder = this.memberBankCard.cardNumber ? true : false;
            } else {
                let { data } = await http.getCreatorCardInfo({
                    searchDataType: this.Ach ? 0 : 1
                });
                //creator  not available  broker card ,  Filtered broker card
                this.creatorCardList =
                    data && data.bankCardList
                        ? data.bankCardList.filter((i) => i.cardOwnerType != "broker")
                        : [];
                //  no card
                // this.creatorCardList = [];
                this.canPlaceOrder = this.creatorCardList.length > 0 ? true : false;
            }
            await this.getTodayCharge();
            this.isLoading = false;
        },
        formatToTwoDecimalsLocale: utils.formatToTwoDecimalsLocale,
        async readTeamOfUse() {
            let param = {
                showAgree: true
            };
            if (!this.isRead) {
                const res = await TermOfUsePop.createTermOfUsePop(param);
                if (!this.isRead) {
                    this.isRead = res.flag;
                    return res.flag;
                }
            }
            return true;
        },
        close() {
            this.isShow = false;
            this.$emit("close");
        },

        // Reset after successfully binding the card card information
        setMyCard() {
            if (!this.newCard?.cardType) {
                return;
            }
            var card = {
                cardType: this.newCard.cardType,
                holderName: this.newCard.holderName,
                cardNumber:
                    this.newCard.cardNumber.length > 12
                        ? this.newCard.cardNumber.slice(12)
                        : this.newCard.cardNumber,
                cardOwnerType: this.isCreator ? "creator" : "member"
            };
            if (this.isCreator) {
                this.creatorCardList = [card];
            } else {
                this.memberBankCard = card;
            }
        },
        setNewCard(card) {
            this.newCard = card;
            this.setMyCard();
        },
        //Policy Whether to check
        changeIsRead(checked) {
            this.isRead = checked;
        },
        // click pay now Buy
        async placeOrder() {
            const isAgree = await this.readTeamOfUse(false);
            if (!isAgree) {
                return;
            }
            if (!this.canPlaceOrder || !this.isRead || this.isLoading) {
                return;
            }
            let tip = payTipPop.createTipPop({ type: "loading" });
            // Bind a card without a card ， Purchase after successfully binding the card

            let paymentRef = this.isCreator ? this.$refs.creatorPay : this.$refs.memberPay;
            let cardPromise = Promise.resolve(0);
            if (paymentRef) {
                let { needBindCard = false, newBindCardId = 0 } = paymentRef.getNewBindCard();
                cardPromise = needBindCard
                    ? paymentRef.bindCard().then((d) => d.id)
                    : Promise.resolve(newBindCardId);
            }
            cardPromise.then((payMethodId) => {
                if (payMethodId) {
                    this.setMyCard();
                }
                // After successfully binding the card
                this.buyTeamAddon(tip, { payMethodId });
            });
        },

        // buy package
        buyTeamAddon(tip, otherParams = {}) {
            const { userId } = infoData.getUserInfo();
            let params = {
                payUserId: userId,
                userId,
                price: this.totalPrice, // Pass the full amount in this field
                teamPackageId: teamPackageIdMap[this.checkedPkg] // 0, 1, 2
            };
            if (otherParams.payMethodId) {
                params.payMethodId = otherParams.payMethodId;
            }
            http.buyTeamAddon(params)
                .then((res) => {
                    if (res && res.data && res.errorCode == 0) {
                        // successful purchase
                        this.close();
                        tip.close();

                        let isAchPaid = this.memberBankCard?.cardType === "ACH";
                        if (this.isCreator) {
                            isAchPaid = this.creatorCardList[0]?.cardType === "ACH";
                        }

                        if (isAchPaid) {
                            tip = payTipPop.createTipPop({
                                type: "active",
                                typeDes: this.$st("common", "paymentInfo.successMsg.achPaySuccess")
                            });
                        } else {
                            tip = payTipPop.createTipPop({
                                type: "success",
                                typeDes: `<p class="redirect-desc">${this.$t(
                                    "teamAddOn.successTip"
                                )}</p>`
                            });
                        }

                        setTimeout(() => {
                            tip.close();
                            this.$emit("ok");
                        }, 2000);
                    } else {
                        this.close();
                        tip.close();
                        let html = `<p class="redirect-desc">${
                            res.errorMsg || this.$t("teamAddOn.failTip")
                        }</span></p>`;
                        tip = payTipPop.createTipPop({
                            type: "fail",
                            typeDes: html
                        });
                        setTimeout(() => {
                            tip.close();
                        }, 2000);
                    }
                })
                .catch((err) => {
                    console.log(err);
                    tip.close();
                    this.close();
                    let html = `<p class="redirect-desc">${this.$t(
                        "teamAddOn.failTip"
                    )}</span></p>`;
                    tip = payTipPop.createTipPop({
                        type: "fail",
                        typeDes: html
                    });
                    setTimeout(() => {
                        tip.close();
                    }, 2000);
                });
        },

        choosePkg(cardData) {
            this.checkedPkg = cardData.type;
            this.getTodayCharge();
        },
        //  Get the prorated amount
        getTodayCharge() {
            return http.getTodayCharge(this.totalPrice).then((res) => {
                this.totalCharge = Number(res).toFixed(2);
            });
        }
    },
    computed: {
        isNewObDashboard() {
            return infoData.isNewObDashboard;
        },
        totalTax({ taxInfo }) {
            return taxInfo.totalTax || 0;
        },
        displayedTotalCharge({ totalCharge, totalTax }) {
            return utils.add([totalCharge, totalTax]);
        },
        //  full package
        totalPrice({ checkedPkg, isLPTAccount }) {
            let curPrice = 0;
            switch (checkedPkg) {
                case "crmonly":
                    curPrice = 99;
                    break;
                case "singleSeat":
                    curPrice = isLPTAccount ? 49 : 149;
                    break;
                case "crmidx":
                    curPrice = 199;
                    break;

                default:
                    break;
            }

            return curPrice;
        },
        // Is there no card
        noCard() {
            if (this.isCreator) {
                return this.creatorCardList.length == 0;
            } else {
                return !this.memberBankCard.cardType;
            }
        },
        //buy now button hover  hint
        buyTip() {
            if (!this.isRead || !this.canPlaceOrder) {
                return !this.canPlaceOrder
                    ? this.$t("teamAddOn.wrongTip1")
                    : this.$t("teamAddOn.wrongTip2");
            } else {
                return "";
            }
        },
        // Copywriting in the card binding component
        bindCardText() {
            return {
                desc: this.$t("teamAddOn.paymentInfo.des2"),
                notes: []
            };
        },
        isCreator() {
            return infoData.getUserInfo().isTeamOwner;
        },
        isLPTAccount() {
            const { LPT } = basePermission;
            return !!LPT;
        },
        hideSingleSeatCard() {
            const { HideSingleSeatCard } = basePermission;
            return !!HideSingleSeatCard;
        },
        cardList({ isLPTAccount, hideSingleSeatCard }) {
            return [
                {
                    show: true,
                    cls: "green",
                    title: this.$t("teamAddOn.card.title1"),
                    titleIcon: "people_04",
                    originalPriceDes: this.$t("teamAddOn.card.originalPrice1"),
                    priceDes: this.$t("teamAddOn.card.price1"),
                    price: "99",
                    desList: [
                        this.$t("teamAddOn.card.des1"),
                        this.$t("teamAddOn.card.des7"),
                        this.$t("teamAddOn.card.des3"),
                        this.$t("teamAddOn.card.des5")
                    ],
                    checked: true,
                    hasSpecialTip: isLPTAccount,
                    type: "crmonly"
                },
                {
                    show: !hideSingleSeatCard,
                    cls: "orange",
                    title: this.$t("teamAddOn.card.title2"),
                    titleIcon: "people_02",
                    originalPriceDes: this.$t("teamAddOn.card.originalPrice3"),
                    priceDes: isLPTAccount
                        ? this.$t("teamAddOn.card.price31")
                        : this.$t("teamAddOn.card.price3"),
                    price: isLPTAccount ? "49" : "149",
                    specialTag: isLPTAccount ? this.$t("teamAddOn.card.specialTag3") : "",
                    specialTip: "",
                    desList: [
                        this.$t("teamAddOn.card.des8"),
                        this.$t("teamAddOn.card.des8-1"),
                        this.$t("teamAddOn.card.des8-2"),
                        this.$t("teamAddOn.card.des8-3")
                    ],
                    checked: true,
                    hasSpecialTip: isLPTAccount,
                    type: "singleSeat"
                },
                {
                    show: true,
                    cls: "primary",
                    title: this.$t("teamAddOn.card.title2"),
                    titleIcon: "people_04",
                    originalPriceDes: this.$t("teamAddOn.card.originalPrice2"),
                    priceDes: this.$t("teamAddOn.card.price2"),
                    price: "199",
                    desList: [
                        this.$t("teamAddOn.card.des11"),
                        this.$t("teamAddOn.card.des7"),
                        this.$t("teamAddOn.card.des12"),
                        this.$t("teamAddOn.card.des3"),
                        this.$t("teamAddOn.card.des5")
                    ],
                    checked: false,
                    hasSpecialTip: isLPTAccount,
                    type: "crmidx"
                }
            ];
        },
        realCardList({ cardList }) {
            return cardList.filter((item) => item.show);
        }
    }
};
</script>

<style lang="less">
.addon-pop {
    .pop-addon-content {
        padding: 20px 30px;
        box-sizing: border-box;
        min-height: 400px;
        width: 100%;
        .content-title {
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            color: #515666;
        }
        .content-desc {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #515666;
            margin-top: 10px;
            margin-bottom: 20px;
        }
        .card-container {
            display: flex;
            .pick-card-wrap {
                &:not(:first-child) {
                    margin-left: 15px;
                }
            }
        }
        .payment-info {
            height: 240px;
            margin-top: 20px;
            .payment-v1-container {
                min-height: fit-content;
                margin-bottom: 10px;
            }
            .form-item.small {
                width: 50%;
                margin-top: 10px;
                max-height: 70px;
            }
            .desc {
                font-size: 12px;
                line-height: 1.5;
                color: #797e8b;
                margin: 6px 0 10px 0;
                font-weight: 100;
            }
            .pay-tip {
                display: none;
            }
            .payment-tip {
                font-size: 12px;
                line-height: 1.5;
                color: #797e8b;
                .blue-word {
                    color: var(--primary-color);
                }
            }
            .margin-style {
                margin-top: 15px;
            }
        }
        .no-card-info {
            height: 335px;
            margin-top: 24px;
            .code-instruction {
                z-index: 1000;
                position: absolute;
                top: -170px;
                right: -28px;
                width: 351px;
                background-color: #ffffff;
                box-shadow: 0 2px 5px 0 rgba(0, 10, 30, 0.1);
                border: solid 1px #c6c8d1;
                margin-top: 0;
                padding: 20px;
                border-radius: 5px;
                &::before {
                    content: "";
                    display: inline-block;
                    width: 216px;
                    height: 120px;
                    background: url(//static.chimeroi.com/crm/images/pay/bubble.png);
                }
                &::after {
                    content: "";
                    width: 10px;
                    height: 10px;
                    background: #fff;
                    position: absolute;
                    top: 160px;
                    right: 28px;
                    transform: rotate(225deg);
                    border: solid 1px #c6c8d1;
                    border-bottom: none;
                    border-right: none;
                }
                div {
                    float: right;
                    width: 130px;
                    margin-top: 18px;
                    p:nth-of-type(1) {
                        font-size: 14px;
                        font-weight: 500;
                        text-align: left;
                        color: #515666;
                    }
                    p:nth-of-type(2) {
                        margin-top: 4px;
                        font-size: 12px;
                        text-align: left;
                        color: #afafb5;
                    }
                }
            }
            .item-label {
                font-size: 14px;
                color: #515666;
                font-weight: inherit;
            }
            .tip {
                font-size: 14px;
                font-weight: 600;
                color: #515666;
                .content {
                    font-size: 12px;
                    line-height: 1.33;
                    color: #a0a3af;
                    font-weight: normal;
                }
            }
            .com-datatable {
                .form-item {
                    &:nth-child(3) {
                        padding-right: 0;
                    }
                }
                .icon-attention_02 {
                    margin-left: 5px;
                }
            }
        }
        .payment-features {
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            padding: 20px;
            min-height: 106px;
            border-radius: 5px;
            background: rgba(36, 146, 252, 0.05);
            .payment-info {
                flex: 0.4;
            }
            .features {
                flex: 0.5;
                padding-right: 40px;
            }
            .info-title {
                font-weight: 600;
                font-size: 14px;
                line-height: 17px;
                color: #515666;
                margin-bottom: 10px;
            }
            .info-row {
                display: flex;
                align-items: center;
                padding-left: 10px;
                .left-round {
                    height: 4px;
                    width: 4px;
                    border-radius: 50%;
                    background-color: #a0a3af;
                }
                .right-text {
                    margin-left: 10px;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 18px;
                    color: #a0a3af;
                    white-space: nowrap;
                }
                .warp-text {
                    max-width: 390px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }
        }
        .content-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            span {
                font-weight: 600;
                font-size: 14px;
                line-height: 17px;
                color: #515666;
            }
            .right-number-count {
                margin-left: 27px;
                display: flex;
                .minus-plus {
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    border: 1px solid #e1e2e6;
                    text-align: center;
                    line-height: 30px;
                    cursor: pointer;
                    box-sizing: border-box;
                    color: #c6c8d1;
                    &.disable {
                        cursor: not-allowed;
                        pointer-events: none;
                        opacity: 0.6;
                    }
                }
                .left-minus {
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                }
                .right-plus {
                    border-top-right-radius: 4px;
                    border-bottom-right-radius: 4px;
                }
            }
            :deep(.chime-input) {
                border-radius: unset;
                height: 30px;
                border: unset;
                border-top: 1px solid #e1e2e6;
                border-bottom: 1px solid #e1e2e6;
                width: 60px;
                text-align: center;
            }
            :deep(.icon2017) {
                font-size: 12px;
                color: #c6c8d1;
                transform: scale(0.833333);
                display: inline-block;
                height: 10px;
            }
        }
        .content-tip {
            height: 32px;
            background: #fef8e4;
            border: 1px solid #faefc0;
            border-radius: 4px;
            padding: 7px 20px;
            margin-top: 20px;
            p {
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                color: #ffa200;
            }
        }
    }
    .pop-footer {
        .addon-charge-info {
            .charge-drop {
                margin-left: 10px;
            }
            border-top: solid 4px var(--primary-color-6);
            .com-pay-charge-info {
                border-top: none;
                box-shadow: 0 0 0 0;
                height: 120px;
                .moeny {
                    font-size: 36px;
                    margin-left: 5px;
                }
                .pay-word {
                    font-size: 18px;
                }
                .pay-agree {
                    font-size: 12px;
                }
            }
            .btn-wrap {
                margin-right: 30px;
                display: flex;
            }
            .place-order-bottom {
                margin-left: 30px;
                .policy {
                    font-size: 0px;
                    display: flex;
                    .pay-agree {
                        max-width: 680px;
                    }
                }
            }
            .primary {
                margin-left: 10px;
            }
        }
        .disabledClick {
            opacity: 0.4;
            cursor: not-allowed;
        }
    }
}
</style>
