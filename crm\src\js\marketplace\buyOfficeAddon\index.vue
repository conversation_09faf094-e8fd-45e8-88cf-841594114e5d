<template>
    <div v-loading="loading">
        <BuyCommon
            ref="buyCommonRef"
            class="buy-office-addon-wrap"
            @buy="buyHandle"
            :disableCheck="loading"
            :isShowAch="true"
            :showTax="showTax"
            taxIconStyle="left:130px"
            section=""
            @cardInfoLoaded="updateTaxInfo"
            @paymentInfoUpdated="updateTaxInfo"
        >
            <template v-slot:content>
                <div class="title mb20">
                    {{ $t("buyOfficeAddon.officeAddon") }}
                </div>
                <div class="addon-table">
                    <div class="table-header">
                        <div class="table-col">
                            {{ $t("buyOfficeAddon.office") }}
                        </div>
                        <div class="table-col">
                            {{ tableQuantityWord }}
                        </div>
                        <div class="table-col" v-if="isRealPackage">
                            {{ $t("buyOfficeAddon.amount") }}
                        </div>
                    </div>
                    <div class="table-row" v-if="isRealPackage">
                        <div class="table-col">
                            {{ $t("buyOfficeAddon.setUpFee") }}
                        </div>
                        <div class="table-col">
                            {{ productInfo.purchaseQty }}
                        </div>
                        <div class="table-col">
                            {{ currencySymbol }}{{ calcResult.setUpPrice || 0 }}
                        </div>
                    </div>
                    <div class="table-row">
                        <div class="table-col">
                            {{ productInfo.name || $t("buyOfficeAddon.officeAddon") }}
                        </div>
                        <div class="table-col">
                            {{ productInfo.purchaseQty }}
                        </div>
                        <div class="table-col" v-if="isRealPackage">
                            {{ currencySymbol }}{{ calcResult.packagePrice || 0
                            }}{{ $t("buyOfficeAddon.monthUnit")
                            }}<span
                                v-if="officeChargePlanServiceId === 'Office_Addon_For_Brokerage'"
                                >({{ $t("buyOfficeAddon.specDes") }})</span
                            >
                        </div>
                    </div>

                    <template v-if="isRealPackage">
                        <div class="table-row" v-if="canInputNumber">
                            <div class="table-col">
                                {{ $t("buyOfficeAddon.seatFee") }}
                            </div>
                            <div class="table-col">
                                <InputNumber
                                    v-model="additionalSeat"
                                    :min="0"
                                    :max="9999"
                                    :step="1"
                                    :showUnit="false"
                                ></InputNumber>
                            </div>
                            <div class="table-col" v-if="isRealPackage">
                                {{ currencySymbol }}{{ calcSeatResult.monthlyTotalBudget || 0 }}
                            </div>
                        </div>
                    </template>

                    <template v-if="!isRealPackage">
                        <div class="table-footer">
                            {{ _today }} - {{ _theLastDayThisMonth }}:
                            <span class="blue"
                                >{{ currencySymbol }}{{ calcResult.realTimePrice }}</span
                            >
                        </div>
                        <div class="table-footer">
                            After {{ _theLastDayThisMonth }} Total:
                            <span class="blue"
                                >{{ currencySymbol }}{{ calcResult.discountedPrice }}/mo</span
                            >
                        </div>
                    </template>
                </div>
            </template>

            <template v-slot:chargeInfo>
                <div class="charge-info">
                    <p class="pay-charge">
                        <span class="pay-word">{{
                            $st("campaigns-leadEngine", "paymentV2.chargeText")
                        }}</span>
                        <span class="money mr10">
                            {{ currencySymbol }}{{ priceIncludingTax }}
                        </span>

                        <DropDown
                            v-if="showTax"
                            class="charge-drop"
                            :noBorder="true"
                            :clickMode="true"
                            placement="top"
                            dropdownCls="tax-drop-cls"
                            style="display: inline-block"
                        >
                            <template v-slot:body="data">
                                <div class="tax-detail-btn flex-row">
                                    <span>{{ $st("common", "chargeInfo.taxDetail") }}</span>
                                    <span
                                        class="tax-detail-btn-icon icon2017 icon-arrow_01_down"
                                        :class="{
                                            'icon-arrow_01_up': data.isOpen
                                        }"
                                    ></span>
                                </div>
                            </template>
                            <template v-slot:dropdown>
                                <div class="charge-info">
                                    <span class="tip-arrow"></span>
                                    <div class="charge-detail" v-html="taxTip"></div>
                                </div>
                            </template>
                        </DropDown>
                    </p>
                </div>
            </template>
        </BuyCommon>
    </div>
</template>

<script setup>
import { debounce } from "lodash";
import { ref, onMounted, computed, watch } from "vue";
import { globalization, components, utils } from "common";
import { crmUtils, infoData, payTipPop, commonHooks, basePermission, globalApi } from "crm";
import BuyCommon from "@/js/common-module/BuyCommon/index.vue";
import { CreateProduct } from "@/js/common-module/OfficeAddOn/const.js";
import http from "./api.js";
import useTax from "@/js/common-module/hooks/useTax.js";
import { useCurrencySymbol } from "@/hooks";

const { showTax, taxInfo, openDetailBefore } = useTax();
const currencySymbol = useCurrencySymbol();

const { $t, $st } = globalization.getT("marketPlace");

const { useOfficeAddon } = commonHooks;
const { InputNumber, DropDown } = components;

const buyCommonRef = ref(null);

const loading = ref(false);
const productInfo = ref(new CreateProduct());
// Office_Add_On
// Office_Addon_For_Brokerage
// Office_Addon_For_Team
// Office_Addon_For_Agent

// Office_Addon_For_Agent_Charge_Rule
// Office_Addon_For_Team_Charge_Rule
// Office_Addon_For_Brokerage_Charge_Rule

// Set_Up_Fee_Agent
// Set_Up_Fee_For_Team
// Set_Up_Fee_Brokerage

const minCount = ref(1);
const officeId = ref("");
const officeChargePlanServiceId = ref("Office_Add_On");
const additionalSeat = ref(0);
const packageList = ref([]);
// const calcResult = ref({
//     discountedPrice: 0,
//     originalPrice: 0,
//     originalRealTimePrice: 0,
//     realTimePrice: 0
// })

const calcOfficeResult = ref({
    discountedPrice: 0,
    originalPrice: 0,
    originalRealTimePrice: 0,
    realTimePrice: 0
});

const calcSeatResult = ref({
    immediatelyPayPrice: 0,
    discountPrice: 0,
    price: 0
});

const officeAddonKeys = computed(() => {
    return (
        {
            Office_Add_On: {
                chargePlanServiceId: "Office_Add_On"
            },
            Office_Addon_For_Brokerage: {
                chargePlanServiceId: "Office_Addon_For_Brokerage",
                planRuleId: "Office_Addon_For_Brokerage_Charge_Rule",
                setUpRuleId: "Set_Up_Fee_Brokerage"
            },
            Office_Addon_For_Agent: {
                chargePlanServiceId: "Office_Addon_For_Agent",
                planRuleId: "Office_Addon_For_Agent_Charge_Rule",
                setUpRuleId: "Set_Up_Fee_Agent"
            },
            Office_Addon_For_Team: {
                chargePlanServiceId: "Office_Addon_For_Team",
                planRuleId: "Office_Addon_For_Team_Charge_Rule",
                setUpRuleId: "Set_Up_Fee_For_Team"
            }
        }[officeChargePlanServiceId.value] || {
            chargePlanServiceId: "Office_Add_On"
        }
    );
});

const calcResult = computed(() => {
    if (officeChargePlanServiceId.value === "Office_Add_On") {
        return {
            ...calcOfficeResult.value,
            packagePrice: calcOfficeResult.value.discountPrice
        };
    } else {
        let officeAddonPriceList = calcOfficeResult.value?.chargeRulePriceList || [];
        let setUpPrice =
            officeAddonPriceList.find(
                (d) => d.pricingServiceId === officeAddonKeys.value.setUpRuleId
            )?.discountedPrice || 0;
        let packagePrice =
            officeAddonPriceList.find(
                (d) => d.pricingServiceId === officeAddonKeys.value.planRuleId
            )?.discountedPrice || 0;
        if (!canInputNumber.value) {
            return {
                ...calcOfficeResult.value,
                setUpPrice,
                packagePrice
            };
        } else {
            let realTimePrice =
                Number(calcOfficeResult.value.realTimePrice || 0) +
                Number(calcSeatResult.value.immediatelyBudget || 0);
            return {
                realTimePrice,
                packagePrice,
                setUpPrice
            };
        }
    }
});

const _today = computed(() => {
    return crmUtils.getFormatDate(new Date().getTime());
});

const canInputNumber = computed(() => {
    return ["Office_Addon_For_Team"].includes(officeChargePlanServiceId.value);
});

const tableQuantityWord = computed(() => {
    // if (canInputNumber.value) {
    //     return $t("buyOfficeAddon.AdditionalQuantity")
    // } else
    if (isRealPackage.value) {
        return $t("buyOfficeAddon.quantity");
    } else {
        return $t("buyOfficeAddon.number");
    }
});

const realSeat = computed(() => {
    if (canInputNumber.value) {
        return Number(additionalSeat.value) + 1;
    }
    return 1;
});

const isSpecialLogic = computed(() => {
    const { isOldOfficeAccount } = commonHooks.useGlobalHooks;
    const { isTeamOwner, isBrokerTeam } = infoData.getUserInfo();
    if (isOldOfficeAccount.value) {
        return false;
    } else if (!isOldOfficeAccount.value && isTeamOwner && !isBrokerTeam) {
        return false;
    } else {
        return true;
    }
});

const taxTip = computed(() => {
    return `<p>
                <span>${$st("campaigns-leadEngine", "paymentV2.chargeText")}</span>
                <span> ${currencySymbol}${utils.formatToTwoDecimalsLocale(
        taxInfo.value?.totalAmount || 0
    )}</span>
            </p>
            <p>
                <span>${$st("common", "chargeInfo.totalTaxes")}:</span>
                <span>${currencySymbol}${utils.formatToTwoDecimalsLocale(
        taxInfo.value?.totalTax || 0
    )}</span>
            </p>`;
});

const priceIncludingTax = computed(() => {
    let num = utils.add([calcResult.value.realTimePrice, taxInfo.value?.totalTax]);
    return utils.formatToTwoDecimalsLocale(num);
});

watch(realSeat, () => {
    calSeatBudget();
});

const _theLastDayThisMonth = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    const nextMonth = new Date(year, month + 1, 1);

    const endOfMonth = new Date(nextMonth - 1);

    return crmUtils.getFormatDate(endOfMonth.getTime());
});

const isRealPackage = computed(() => {
    let { OfficeAddonDistribution } = basePermission;
    return OfficeAddonDistribution;
});

async function buyHandle({ payMethodId }) {
    loading.value = true;
    if (!officeId.value) {
        officeId.value = crmUtils.getParameterByName("officeId");
    }
    const params = {
        purchaseQty: productInfo.value.purchaseQty,
        officeId: officeId.value,
        chargePlanServiceId: officeChargePlanServiceId.value,
        seat: additionalSeat.value
    };

    payMethodId && (params.payMethodId = payMethodId);

    const buyRes = await http.buyOfficeAddOn(params).catch((err) => {
        console.log("buy Office Addon error", err);
        return err;
    });

    if (buyRes?.status?.code === 0) {
        useOfficeAddon.updateOfficeAddon();

        if (!canInputNumber.value && additionalSeat.value > 0) {
            loading.value = false;
            payTipPop.createTipPop({
                type: "success",
                typeDes: $st("common", "pay.successDesc"),
                autoClose: 2000
            });

            gotoPage();
        } else {
            if (isSpecialLogic.value) {
                http.saveRemoveSeatsPurchase(params)
                    .then((res) => {
                        if (res?.errorCode === 1000000 || res?.status.code === 0) {
                            payTipPop.createTipPop({
                                type: "success",
                                typeDes: $st("common", "pay.successDesc"),
                                autoClose: 2000
                            });
                            gotoPage();
                        } else {
                            payTipPop.createTipPop({
                                type: "fail",
                                typeDes: $st("common", "pay.payFailed"),
                                autoClose: 5000
                            });
                        }
                    })
                    .catch((err) => {
                        console.log("buy Office Addon seat error", err);
                        return err;
                    })
                    .finally(() => {
                        loading.value = false;
                    });
            } else {
                params.total = calcSeatResult.value?.price ?? calcSeatResult.value?.discountPrice;
                const { paymentInfo, userId } = infoData.getUserInfo();
                const subscribeCode = paymentInfo?.subscribeCode || null;
                const curParams = {
                    seatCount: params.seat,
                    subscribeCode,
                    agentId: userId
                };

                globalApi
                    .toBuySeat(curParams)
                    .then((res) => {
                        if (res) {
                            gotoPage();
                        }
                    })
                    .catch((err) => {
                        console.log("buy Office Addon seat error", err);
                        return err;
                    })
                    .finally(() => {
                        loading.value = false;
                    });
            }
        }
    } else {
        loading.value = false;
    }
}

function gotoPage() {
    if (
        ["Office_Addon_For_Team", "Office_Addon_For_Agent"].includes(
            officeChargePlanServiceId.value
        )
    ) {
        window.__crmRouter.push({
            name: "officeAddonContractReview",
            query: {
                price: productInfo.value.discountPrice,
                productName: productInfo.value.name,
                productId: officeChargePlanServiceId.value
            }
        });
    } else {
        window.__crmRouter.push({
            name: "setting_organization",
            query: {
                needReload: 1
            }
        });
    }
}

function initAddon() {
    let currentPackage =
        packageList.value.find((d) => d.chargePlanServiceId === officeChargePlanServiceId.value) ||
        {};

    productInfo.value = new CreateProduct({
        ...currentPackage,
        purchaseQty: minCount.value,
        name: currentPackage?.chargePlanName
    });
}

function getPackageList() {
    return http.getOfficePackageInfo().then((res) => {
        if (res?.status?.code === 0 && res?.data) {
            packageList.value = res.data;
        }
    });
}

async function checkAuth() {
    const UrlOfficeId = crmUtils.getParameterByName("officeId");
    if (UrlOfficeId) {
        officeId.value = UrlOfficeId;
    }

    const UrlChargePlanServiceId = crmUtils.getParameterByName("chargePlanServiceId");
    if (UrlChargePlanServiceId) {
        officeChargePlanServiceId.value = UrlChargePlanServiceId;
    }
    const invitationIdEncode = crmUtils.getParameterByName("invitationEncode");
    if (!invitationIdEncode) {
        return true;
    }
    let [err, res] = await crmUtils.awaitWrap(http.checkBuyAuth(invitationIdEncode));
    if (err) {
        return false;
    }
    if (res.data?.chargePlanServiceId) {
        officeChargePlanServiceId.value = res.data?.chargePlanServiceId;
    }
    if (res.data?.officeId) {
        officeId.value = res.data?.officeId;
        return true;
    }
}

const calcPrice = debounce(calcPriceFunc, 200);
async function calcPriceFunc() {
    const res = await http
        .calcOfficeAddOn({
            purchaseQty: productInfo.value.purchaseQty,
            officeId: officeId.value,
            chargePlanServiceId: officeChargePlanServiceId.value
        })
        .catch((err) => {
            console.log("calc Office Addon error", err);
            return err;
        });

    if (res?.status?.code === 0) {
        calcOfficeResult.value = res.data[0];
        updateTaxInfo();
    }
}

const calSeatBudget = debounce(calSeatBudgetFunc, 200);
async function calSeatBudgetFunc() {
    const res = await http.calSeatBudget(additionalSeat.value).catch((err) => {
        console.log("calc seat error", err);
        return err;
    });

    if (res?.status?.code === 0) {
        calcSeatResult.value = res.data;
        updateTaxInfo();
    }
}

async function updateTaxInfo() {
    if (!showTax.value) {
        return;
    }
    const paymentRef = buyCommonRef.value?.getPaymentRef?.();

    const res = await openDetailBefore({
        paymentRef: paymentRef,
        taxItems: [
            {
                productServiceId: "PR#OFFICE_ADDON",
                amount: calcOfficeResult.value.realTimePrice || 0,
                quantity: 1
            },
            {
                productServiceId: "Seats",
                amount: calcSeatResult.value.immediatelyBudget || 0,
                quantity: 1
            }
        ]
    });
    return res;
}

onMounted(async () => {
    loading.value = true;
    const haveAuth = await checkAuth();
    loading.value = false;
    if (!haveAuth) {
        window.__crmRouter.push({
            name: "home"
        });
        return;
    }
    await getPackageList();

    initAddon();

    calcPrice();
});
</script>

<style lang="less" scoped>
.buy-office-addon-wrap {
    .title {
        font-weight: 700;
        font-size: 24px;
        line-height: 36px;
        color: #202437;
        // font-family: SF Pro;
    }
    .sub-title {
        font-weight: 700;
        font-size: 18px;
        line-height: 36px;
        color: #202437;
        // font-family: SF Pro;
    }
    .addon-table {
        margin-bottom: 40px;
        border: 1px solid #e1e2e6;
        border-radius: 4px;
        background: #fff;
        .table-header,
        .table-row {
            display: flex;
        }
        .table-header {
            color: #202437;
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            border-bottom: 1px solid #ebecf1;
        }
        .table-row {
            color: #515666;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            border-bottom: 1px solid #ebecf1;
            &:last-child {
                border-bottom: none;
            }
        }
        .table-col {
            width: 50%;
            padding: 12px 20px;
            &:first-child {
                border-right: 1px solid #ebecf1;
            }
        }
        .table-footer {
            padding: 16px 20px;
            font-weight: 700;
            font-size: 16px;
            line-height: 20px;
            text-align: right;
            color: #515666;
            border-top: 1px solid #ebecf1;
        }
        .blue {
            color: var(--primary-color);
        }
    }

    .pay-charge {
        font-size: 20px;
        line-height: 24px;
        font-weight: 700;
        .pay-word {
            color: #202437;
            margin-right: 5px;
        }
        .money {
            color: var(--primary-color);
            font-size: 40px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
        }
    }
}
</style>
