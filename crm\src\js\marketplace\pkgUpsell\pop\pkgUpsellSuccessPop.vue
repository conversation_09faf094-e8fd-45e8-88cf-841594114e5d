<template>
    <PopWin
        class="fu-success-pop"
        :isShow="isShow"
        title=""
        @close="clickOk('close')"
        :isUserScroller="false"
    >
        <template v-slot:content>
            <div class="fu-pop-content">
                <div class="icon2017 icon-success_02"></div>
                <p class="pop-desc">
                    {{ $t("descripe3", { value: type }) }}
                </p>
                <div class="link-content">
                    <p class="link-desc">
                        {{ $t("descripe4") }}
                    </p>
                    <ul class="link-list">
                        <li class="link-detail">
                            <a target="_blank" :href="`${origin}/admin/home/<USER>/agent`">{{
                                $t("descripe5")
                            }}</a>
                        </li>
                        <li class="link-detail">
                            <a
                                target="_blank"
                                :href="`${origin}/admin/home/<USER>/manageDialer`"
                                >{{ $t("descripe6") }}</a
                            >
                        </li>
                        <li class="link-detail" v-if="type !== 'Team'">
                            <a
                                target="_blank"
                                :href="`${origin}/admin/home/<USER>/AIAssistant`"
                                >{{ $t("descripe7") }}</a
                            >
                        </li>
                    </ul>
                </div>
                <div class="footer">
                    <button class="chime-btn primary" @click="clickOk('ok')">
                        {{ $st("common", "ok") }}
                    </button>
                </div>
            </div>
        </template>
    </PopWin>
</template>
<script>
import { components } from "common";
const { PopWin } = components;
export default {
    langModule: "pkgUpsell",
    data() {
        return {
            isShow: false,
            type: "Team",
            origin: window.location.origin
        };
    },
    components: {
        PopWin
    },
    methods: {
        init(config) {
            this.type = config.type;
            this.isShow = true;
        },
        clickOk(type) {
            this.$emit(type);
            this.isShow = false;
        }
    }
};
</script>
<style lang="less">
.fu-success-pop {
    --clear-blue: var(--primary-color);
    .fu-pop-content {
        width: 500px;
        font-family: "SF UI Text";
        padding: 30px;
        > .icon2017 {
            font-size: 60px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 55px;
            letter-spacing: normal;
            text-align: center;
            color: #20c472;
            text-align: center;
        }
        .pop-desc {
            margin-top: 30px;
            font-size: 18px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: normal;
            text-align: center;
            color: #515666;
        }
        .link-content {
            border-top: 1px solid #ebecf1;
            margin-top: 30px;
            .link-desc {
                margin-top: 20px;
                font-size: 14px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #515666;
            }
            .link-list {
                margin-top: 20px;
                .link-detail {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                    &:nth-last-of-type(1) {
                        margin-bottom: 0;
                    }
                    &::before {
                        content: "";
                        display: block;
                        width: 6px;
                        height: 6px;
                        border-radius: 3px;
                        margin-right: 10px;
                        background-color: #e1e2e6;
                    }
                    a {
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: normal;
                        font-stretch: normal;
                        font-style: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: var(--clear-blue);
                        text-decoration: underline;
                    }
                }
            }
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            .chime-btn {
                width: 160px;
                height: 40px;
                border-radius: 4px;
                background-color: var(--clear-blue);
            }
        }
    }
}
</style>
