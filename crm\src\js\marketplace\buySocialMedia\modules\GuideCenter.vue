<template>
    <div class="buySocialMedia__guideCenter">
        <commonSwiper
            class="buySocialMedia__swiper-box"
            :optConfig="optConfig"
            :dataList="source"
            :mouseHover="true"
        >
            <template v-slot:swiperSlide="{ item }">
                <div class="buySocialMedia__swiper-item">
                    <div class="introduce">
                        <div class="title">{{ item.title }}</div>
                        <div class="desc">
                            <p
                                v-for="(desc, index) in item.desc"
                                :key="desc + index"
                                v-html="desc"
                                v-click="{
                                    selector: '.video-link',
                                    callback: playVideo
                                }"
                            ></p>
                        </div>
                    </div>
                    <ChimeImage outClass="swiper-img" :src="item.img" />
                </div>
            </template>
        </commonSwiper>
    </div>
</template>
<script>
import { showVideoPreview } from "crm";

import { components } from "common";
const { Swiper: commonSwiper, ChimeImage } = components;
export default {
    langModule: "marketPlace",
    name: "SocialMediaGuideCenter",
    components: {
        commonSwiper,
        ChimeImage
    },
    props: {
        isPop: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            optConfig: {
                loop: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false
                }
            }
        };
    },
    computed: {
        source({ $t, isPop }) {
            return [
                {
                    title: $t("buySocialMedia.AIPost.title"),
                    desc: [$t("buySocialMedia.AIPost.desc")],
                    img: "https://static.chimeroi.com/servicetool-temp/loftyworks/original_a4e1b5d5-0c91-4e5a-950b-1e44198c02bc.png"
                },
                {
                    title: $t("buySocialMedia.autoPost.title"),
                    desc: [$t("buySocialMedia.autoPost.desc")],
                    img: "https://cdn.chime.me/image/fs/sitebuild/2025217/1/original_e58c870d-a6c3-4e4a-9893-3b0add95d08e.png"
                },
                {
                    title: $t("buySocialMedia.multipleChannels.title"),
                    desc: [$t("buySocialMedia.multipleChannels.desc")],
                    img: isPop
                        ? "https://cdn.chime.me/image/fs/sitebuild/2025218/6/original_00e7ee64-f2ba-4bbd-bc7f-09eb32760ebe.png"
                        : "https://cdn.chime.me/image/fs/sitebuild/2025217/1/original_591aa96e-ff9d-4f4e-8c84-99615aad710f.png"
                },
                {
                    title: $t("buySocialMedia.listingVideos.title"),
                    desc: [
                        $t("buySocialMedia.listingVideos.desc1"),
                        $t("buySocialMedia.listingVideos.desc2")
                    ],
                    img: "https://static.chimeroi.com/servicetool-temp/2025523/7/494c79a505c640d5_loftyworks2/original_85372c6b-6b18-429a-a94b-2c122e9418b2.png"
                },
                {
                    title: $t("buySocialMedia.boostPost.title"),
                    desc: [$t("buySocialMedia.boostPost.desc")],
                    img: isPop
                        ? "https://static.chimeroi.com/servicetool-temp/2025523/7/cec2b8f62c754336_loftyworks2/original_45f1d64e-5fd8-433a-9d1f-79dc406804a6.png"
                        : "https://static.chimeroi.com/servicetool-temp/2025523/7/0692199b3ce34a8f_loftyworks2/original_e1942859-f235-4892-947f-475a930f5792.png"
                }
            ];
        }
    },
    methods: {
        playVideo() {
            showVideoPreview({
                src: "https://cdn.chime.me/doc/fs/upload/20231030/18/9a9edaa1-4f29-4777-bcd8-c16ac9da142d/videodemo.mp4",
                show: true
            });
        }
    }
};
</script>
<style lang="less">
div.buySocialMedia__guideCenter {
    .buySocialMedia__swiper-box {
        margin-top: 20px;
        padding: 40px;
        background: #ffffff;
        border: 1px solid #e1e2e6;
        border-radius: 4px;
        .buySocialMedia__swiper-item {
            display: flex;
            .introduce {
                flex: 1;
                margin-right: 45px;
                .title {
                    font-weight: 700;
                    font-size: 32px;
                    line-height: 48px;
                    color: #202437;
                }
                .desc {
                    margin-top: 15px;
                    font-size: 16px;
                    line-height: 24px;
                    color: #515666;
                    p {
                        line-height: 28px;
                        .color1 {
                            color: #202437;
                        }
                        .video-link {
                            color: var(--primary-color);
                            cursor: pointer;
                            &:hover {
                                text-decoration: underline;
                            }
                        }
                        & + p {
                            margin-top: 10px;
                        }
                    }
                }
            }
            .swiper-img {
                flex-shrink: 0;
                width: 520px;
                height: 260px;
                object-fit: cover;
                margin-bottom: 10px;
            }
        }
        .swiper-pagination {
            left: 0;
            bottom: 0;
            width: fit-content;
            .swiper-pagination-bullet {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin: 0;
                opacity: 0.15;
                background-color: var(--primary-color);
                cursor: pointer;
                & + .swiper-pagination-bullet {
                    margin-left: 10px;
                }
            }
            .swiper-pagination-bullet-active {
                opacity: 1;
                background-color: var(--primary-color);
            }
        }
    }
}
</style>
