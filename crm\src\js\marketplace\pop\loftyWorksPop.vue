<template>
    <PopWin
        :isShow="isShow"
        @close="closePop()"
        class="lofty-work-pop"
        :title="$t('loftyWorks.popTitle')"
    >
        <template v-slot:content>
            <div class="pop-content">
                <div class="description">
                    <VideoPlayer
                        class="description-video"
                        width="360px"
                        height="200px"
                        :videoSrc="videoSrc"
                    >
                    </VideoPlayer>

                    <div class="description-content">
                        <p class="title">{{ $t("loftyWorks.paymentInfo.title") }}</p>

                        <div class="desc">
                            {{ $t("loftyWorks.paymentInfo.des1") }}
                        </div>

                        <div class="desc">
                            {{ $t("loftyWorks.paymentInfo.des2") }}
                        </div>
                    </div>
                </div>

                <div class="service-introduction">
                    <p class="service-introduction_title">
                        {{ $t("eliteCoaching.serviceIntroduction") }}
                    </p>

                    <div class="service-table">
                        <div class="service-table__row">
                            <div class="cell cell-title">{{ $t("eliteCoaching.service") }}</div>

                            <div
                                class="cell cell-content service-item"
                                v-html="productInfo.productDesc"
                            ></div>
                        </div>

                        <div class="service-table__row">
                            <div class="cell cell-title">{{ $t("eliteCoaching.startDate") }}</div>

                            <div class="cell cell-content">{{ productInfo.startDate }}</div>
                        </div>

                        <div class="service-table__row">
                            <div class="cell cell-title">{{ $t("eliteCoaching.pricing") }}</div>

                            <div class="cell cell-content">{{ productInfo.priceText }}</div>
                        </div>

                        <div class="service-table__row">
                            <div class="cell cell-title">
                                {{ $t("eliteCoaching.billingStrategy") }}
                            </div>

                            <div class="cell cell-content">{{ productInfo.billingStrategy }}</div>
                        </div>
                    </div>
                </div>

                <div class="payment-info">
                    <PaymentInfo
                        :card="cardInfo"
                        :canAddAnotherCard="!isNewObDashboard"
                        ref="paymentInfo"
                        :needBindCard="noCard"
                        @input="newCard = $event"
                        :validateResult.sync="canPlaceOrder"
                        :bindCardText="bindCardText"
                        :isShowAch="false"
                    >
                        <template v-slot:cardTip>
                            <div class="pay-tip" v-html="$t('professionalCoaching.payTip')"></div>
                        </template>
                    </PaymentInfo>
                </div>
            </div>
        </template>
        <template v-slot:footer>
            <div class="footer-charge-info">
                <ChargeInfo
                    :stepNum="1"
                    :step="2"
                    :isRead="isRead"
                    :chargeText="$t('chargeDes')"
                    :totalCharge="totalCharge"
                >
                    <template v-slot:policy>
                        <CheckBox :checked="isRead" @change="changeIsRead"></CheckBox>
                        <span
                            class="pay-agree"
                            v-click="{
                                selector: '.tu-link',
                                callback: readTeamOfUse
                            }"
                            v-html="$t('professionalCoaching.payAgree')"
                        ></span>
                    </template>
                    <template v-slot:btn>
                        <div>
                            <button
                                type="button"
                                class="chime-btn primary"
                                @click="payCharge"
                                :class="{
                                    disabledClick: !canPlaceOrder || isLoading
                                }"
                            >
                                {{ $t("payNow") }}
                            </button>
                        </div>
                    </template>
                </ChargeInfo>
            </div>
        </template>
    </PopWin>
</template>

<script setup>
import { computed, ref, defineExpose, defineEmits } from "vue";
import { components, globalization } from "common";
import { PaymentInfo, ChargeInfo, payTipPop, crmUtils, infoData } from "crm";
import http from "../api.js";
import TermOfUsePop from "@/js/common-module/termOfUsePop/index.js";
import pop from "./createPop";
import { useCurrencySymbol } from "@/hooks";

const { PopWin, CheckBox, VideoPlayer } = components;
const { $t } = globalization.getT("marketPlace");

const currencySymbol = useCurrencySymbol();

const emit = defineEmits(["ok"]);

class CreateProduct {
    constructor(options = {}) {
        this.priceText = `${currencySymbol}${options.listPrice || ""}/Month`;
        this.realTimePrice = options.realTimePrice || 0;
        this.startDate = crmUtils.getDate(new Date());
        this.productDesc = options.productDesc || "";
        this.billingStrategy = options.billingStrategy || "";
    }
}

const isShow = ref(false);
const isRead = ref(false);
const isLoading = ref(false);
const canPlaceOrder = ref(false);
const cardInfo = ref({});

const newCard = ref({});
const paymentInfo = ref(null);

const currPack = ref({});

const productInfo = ref(new CreateProduct());

const videoSrc = ref(
    "https://static.chimeroi.com/servicetool-temp/2023919/3/3748fbb5-a9dc-464f-9527-4e6b33ff7a0a_lucido.mp4"
);

const noCard = computed(() => {
    return !cardInfo.value.cardType;
});

const bindCardText = computed(() => {
    return {
        desc: $t("professionalCoaching.paymentInfo.topInfo"),
        notes: []
    };
});

const totalCharge = computed(() => {
    return currPack.value.realTimePrice || 0;
});

async function init() {
    isShow.value = true;

    isLoading.value = true;

    await Promise.all([getCardInfo(), getPackage()]);

    isLoading.value = false;
}
const isNewObDashboard = infoData.isNewObDashboard;
async function getPackage() {
    let res = await http.getLoftyWorkPackage().catch((err) => {
        console.log("get package error:", err);
        return err;
    });

    if (res?.status?.code === 0 && res?.data) {
        productInfo.value = new CreateProduct(res.data);
        currPack.value = res.data;
    }
}

async function getCardInfo() {
    const res = await http.getMemberCardInfo().catch((err) => {
        console.log("Error fetching card info:", err);
        return err;
    });

    let resData = res?.data ?? {};

    cardInfo.value = resData;
    canPlaceOrder.value = !!resData?.cardNumber;
}

function changeIsRead({ checked }) {
    isRead.value = checked;
}

function createTip(type, typeDes, showCloseIcon = false) {
    const tip = payTipPop.createTipPop({ type, typeDes, showCloseIcon });

    return {
        close: () => tip.close()
    };
}

async function readTeamOfUse(isClickTOULink = true) {
    let param = {
        showAgree: !isClickTOULink,
        section: "professional_marketing_service"
    };

    if (isClickTOULink || !isRead.value) {
        const res = await TermOfUsePop.createTermOfUsePop(param);

        if (!isClickTOULink && !isRead.value) {
            isRead.value = res.flag;

            return res.flag;
        }
    }
    return true;
}

function setMyCard() {
    let card = {
        cardType: newCard.value.cardType,
        holderName: newCard.value.holderName,
        cardNumber: newCard.value.cardNumber.slice(12)
    };

    cardInfo.value = card;
}

async function buyHandle(tip, payMethodId) {
    const params = {
        subscribes: [currPack.value]
    };
    payMethodId && (params.payMethodId = payMethodId);
    const buyRes = await http.buyLoftyWork(params).catch((err) => {
        console.log("buy LoftyWork error", err);
        return err;
    });

    tip.close();

    if (buyRes?.status?.code === 0 && buyRes?.data) {
        closePop({ type: true });

        infoData.updateUserInfo({ hasBuyLoftyWorks: true });

        pop.openLoftyWorkTipPop({
            type: "success"
        });
    } else {
        pop.openLoftyWorkTipPop({
            type: "fail",
            title: $t("loftyWorks.payFailTitle")
        });
    }
}

async function payCharge() {
    const isAgree = await readTeamOfUse(false);

    if (!isAgree) {
        return;
    }

    if (!canPlaceOrder.value || !isRead.value || isLoading.value) {
        return;
    }

    let tip = createTip("loading");
    let { needBindCard, newBindCardId } = paymentInfo.value.getNewBindCard();
    const cardPromise = needBindCard
        ? this.$refs.payment.bindCard().then((d) => d.id)
        : Promise.resolve(newBindCardId);
    cardPromise.then((payMethodId) => {
        if (noCard.value) {
            setMyCard();
        }
        buyHandle(tip, payMethodId);
    });
}

function closePop({ type = false } = {}) {
    isShow.value = false;
    emit("ok", { type });
}

defineExpose({
    init
});
</script>

<style lang="less" scoped>
.lofty-work-pop {
    .pop-content {
        width: 800px;
        padding: 20px 30px;
        box-sizing: border-box;
        overflow: auto;

        .description {
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            margin-bottom: 30px;

            &-video {
                flex-shrink: 0;

                :deep(.vjs-big-play-button) {
                    background-color: rgba(255, 255, 255, 0.9);
                    width: 48px;
                    height: 48px;
                    line-height: 48px;
                    transform: translate(-50%, -50%);
                    margin: 0;

                    .vjs-icon-placeholder {
                        color: #202437;
                    }
                }
            }
            &-content {
                margin-left: 30px;
                flex: 1;
                overflow: hidden;
                box-sizing: border-box;
                padding-top: 10px;

                .title {
                    color: #515666;
                    font-size: 20px;
                    font-weight: 700;
                    line-height: 24px;
                    word-break: break-word;
                    margin-bottom: 20px;
                }
                .desc {
                    position: relative;
                    color: #a0a3af;
                    font-size: 14px;
                    line-height: 18px;
                    font-weight: 400;
                    padding-left: 12px;

                    &::before {
                        content: "";
                        display: block;
                        position: absolute;
                        width: 6px;
                        height: 6px;
                        top: 7px;
                        left: 0;
                        background-color: #a0a3af;
                        border-radius: 50%;
                    }
                }
            }
        }
        .service-introduction {
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
            margin-bottom: 20px;

            &_title {
                color: #515666;
                font-size: 14px;
                font-weight: 700;
                line-height: 18px;
                margin-bottom: 10px;
            }
            .service-table {
                width: 100%;
                overflow: hidden;
                box-sizing: border-box;
                border: 1px solid #e1e2e6;
                border-radius: 4px;

                &__row {
                    width: 100%;
                    overflow: hidden;
                    box-sizing: border-box;
                    display: flex;

                    &:nth-of-type(n + 2) {
                        border-top: 1px solid #e1e2e6;
                    }
                    .cell {
                        color: #515666;
                        font-size: 14px;
                        padding: 15px 20px;
                        overflow: hidden;
                        box-sizing: border-box;

                        &:nth-of-type(n + 2) {
                            border-left: 1px solid #e1e2e6;
                        }
                        &-title {
                            width: 150px;
                            flex-shrink: 0;
                        }
                        &-content {
                            flex: 1;

                            &.service-item {
                                word-break: break-word;
                                line-height: 20px;
                            }
                        }
                    }
                }
            }
        }
        .payment-info {
            :deep(.payment-v1-container) {
                .payment-show-card {
                    h3 {
                        color: #515666;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 24px;
                    }
                }
                .pay-tip {
                    margin-top: 13px;
                    text-align: left;
                    font-size: 12px;
                }
                .desc {
                    color: #797e8b;
                    font-size: 12px;
                    padding: 4px 0 10px;
                }
                .form-item.small:nth-child(3) #chime-cvv {
                    min-width: 44% !important;
                }
                .code-instruction {
                    bottom: 38px;
                    top: auto;
                    &::after {
                        transform: rotate(225deg);
                        bottom: -6px;
                        top: auto;
                    }
                }
            }
        }
    }
    .pop-footer {
        .footer-charge-info {
            box-shadow: 0 0 0 0;
            height: 120px;

            :deep(.com-pay-charge-info) {
                box-shadow: 0 0 0 0;
                height: 120px;
                .moeny {
                    font-size: 36px;
                    margin-left: 10px;
                    line-height: 42px;
                }
                .pay-word {
                    font-size: 18px;
                }
                .pay-agree {
                    font-size: 12px;
                }
                .btn-wrap {
                    margin-right: 30px;
                    display: flex;
                }
            }
            :deep(.place-order-bottom) {
                margin-left: 30px;
                .policy {
                    font-size: 0px;
                    display: flex;
                    align-items: center;
                    margin-top: 6px;

                    .pay-agree {
                        max-width: 680px;
                        line-height: 18px;
                    }
                }
            }
        }
        .disabledClick {
            opacity: 0.4;
            cursor: not-allowed;
        }
    }
    .blue-color {
        color: var(--primary-color);
    }
}
</style>
