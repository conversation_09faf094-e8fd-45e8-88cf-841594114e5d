<template>
  <div class="note-panel">
    <div class="select-area">
      <div class="option">
        <span>Note</span>
        <img src="https://via.placeholder.com/150" alt="arrow down" />
      </div>
    </div>
    <div class="right">
      <input type="text" placeholder="Add a note/log" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotePanel',
};
</script>

<style scoped>
.note-panel {
  display: flex;
  align-items: center;
  height: 50px;
  border: 1px solid #ebecf1;
  border-radius: 6px;
}
.select-area {
  padding: 0 10px;
  width: 140px;
}
.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 510;
  color: #515666;
}
.right {
  width: 100%;
  padding-right: 20px;
}
.right input {
  width: 100%;
  height: 36px;
  border: 1px solid #c6c8d1;
  border-radius: 6px;
  padding: 0 10px;
}
</style>