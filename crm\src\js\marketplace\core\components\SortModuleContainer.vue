<template>
    <div style="width: 100%" class="card-group-box" v-if="!isLoading">
        <template v-if="displaySortModuleList.length">
            <template v-for="(sortItem, sortIdx) in displaySortModuleList">
                <div v-if="!sortItem.hidden" :key="'sort' + sortIdx" :class="`three-columns-box`">
                    <div
                        class="category-first-order-title"
                        v-if="showFirstOrderTitle && sortItem.categoryList.length"
                    >
                        <div class="slider"></div>
                        <p>{{ sortItem.title }}</p>
                        <div class="slider"></div>
                    </div>
                    <!-- <SuggestCard v-on="$listeners" v-if="sortItem.belong === 'CUSTOM' && suggestCardList.length" :suggestCardList="suggestCardList"></SuggestCard> -->
                    <div
                        v-for="(moduleItem, cateIdx) in sortItem.categoryList"
                        :key="sortItem.id + '_' + cateIdx"
                        :class="moduleItem.cls"
                    >
                        <div class="sort-title-box">
                            <span class="sortTitle">{{ moduleItem.title }}</span>
                            <span
                                class="app-add-box"
                                v-if="
                                    currentMode === 'editing' &&
                                    canEditType.includes(moduleItem.belong)
                                "
                                @click="openAppAddPop(sortIdx, cateIdx)"
                            >
                                <span class="icon2017 icon-add_bold"></span>
                                <span class="">{{ $t("btns.addNewApp") }}</span>
                            </span>
                        </div>
                        <div
                            class="card-panel"
                            :cateId="moduleItem.id"
                            :class="[moduleItem.belong, currentMode === 'editing' ? '' : 'forbid']"
                        >
                            <template v-if="moduleItem.appList.length === 0">
                                <Empty
                                    :childCate="true"
                                    :belong="moduleItem.belong"
                                    :emptyText="appEmptyText"
                                    :currentMode="currentMode"
                                    @add="openAppAddPop(sortIdx, cateIdx)"
                                ></Empty>
                            </template>
                            <!--  :force-fallback="true" -->
                            <Draggable
                                v-model="moduleItem.appList"
                                :scroll-sensitivity="150"
                                :force-fallback="true"
                                animation="300"
                                group="market-place-card"
                                ghost-class="market-place-card-ghost"
                                chosen-class="market-place-card-chosen"
                                filter=".forbid"
                                :scroll="true"
                                handle=".market-card"
                                :move="move"
                                :style="moduleItem.appList.length === 0 ? minHeightStyle : ''"
                                @end="endHandler"
                            >
                                <transition-group>
                                    <!-- :key="[moduleItem.cls, item.title, index].join()" -->
                                    <!-- :class="item.id==1?'item forbid':'item'"  if not edit state -->
                                    <MarketCard
                                        v-for="(item, appIdx) in moduleItem.appList"
                                        :group="moduleItem.value"
                                        :key="[moduleItem.cls, item.title, item.id, appIdx].join()"
                                        :index="appIdx"
                                        v-bind="item"
                                        :environment="environment"
                                        :currentMode="currentMode"
                                        :btnWrapperClass="getBtnWrapperClass(item)"
                                        :buttonText="item.btnText"
                                        @click="onClick(item)"
                                        :class="
                                            currentMode === 'editing' && canHandle(item)
                                                ? 'edit'
                                                : 'forbid'
                                        "
                                        :discountImg="item.discountImg ? item.discountImg : ''"
                                        :topRightImg="item.topRightImg ? item.topRightImg : ''"
                                        :tags="item.category ? item.category : []"
                                        :tagUrl="item.tagUrl"
                                        :showTitleTip="item.showTitleTip"
                                        :butttonTip="
                                            item.butttonTip && currentMode !== 'normal'
                                                ? item.butttonTip
                                                : {}
                                        "
                                        :btnDisabled="item.btnDisabled ? item.btnDisabled : false"
                                        :connect="item.connect"
                                        :calendarType="item.calendarType"
                                        :value="item.push"
                                        :check="item.push"
                                        :keyName="item.key"
                                        @change="changeHandler(item, $event)"
                                    >
                                        <template #editIconBox>
                                            <!-- TODO  Determine if it can be edited  -->
                                            <div class="icon-box" v-if="canHandle(item)">
                                                <span
                                                    v-if="item.type === 'CUSTOM_APP'"
                                                    class="icon2017 icon-draw_05"
                                                    @click="
                                                        editHandler(item, {
                                                            sortIdx,
                                                            cateIdx,
                                                            appIdx,
                                                            cateItem: moduleItem
                                                        })
                                                    "
                                                ></span>
                                                <span class="icon2017 icon-handle drag-icon"></span>
                                                <span
                                                    v-if="canHandle(item)"
                                                    :class="[
                                                        'icon2017',
                                                        'icon-delete_01',
                                                        'del-icon',
                                                        item.canHide ? '' : 'disabled'
                                                    ]"
                                                    @click="delHandler(sortItem, moduleItem, item)"
                                                    v-tip="hideIconTip(item)"
                                                ></span>
                                            </div>
                                        </template>

                                        <template #additional v-if="hasAdditionalSlot(item)">
                                            <Icon
                                                icon="icon-settings_01"
                                                size="giant"
                                                hasBorder
                                                @trigger="additionalClickHandle(item)"
                                            />
                                        </template>
                                    </MarketCard>
                                </transition-group>
                            </Draggable>
                        </div>
                    </div>
                </div>
            </template>
        </template>
        <Empty style="width: 90%" v-else :emptyText="appEmptyText"></Empty>
    </div>
</template>

<script>
import MarketCard from "./card";
import Draggable from "vuedraggable";
import { popMgr, utils, components } from "common";
import { createVendorAppPop, createAppPop } from "../modules/index.js";
import Empty from "./empty";
import { basePermission, infoData } from "crm";
import { mapState, mapMutations } from "vuex";
import leadPondApi from "@/js/common-module/LeadPondPop/api.js";
import store from "../store/index.js";
import lodash from "lodash";

const { Icon } = components;

export default {
    name: "SortModuleContainer",
    langModule: "marketPlace",
    store,
    components: {
        MarketCard,
        Draggable,
        Empty,
        Icon
    },
    data() {
        return {
            curSortModuleList: []
        };
    },
    props: {
        temporaryHideApps: {
            type: Array,
            default: () => []
        },
        renderApps: {
            type: Array,
            default: () => []
        },
        sortModuleList: {
            type: Array,
            default: () => []
        },
        isLoading: {
            type: Boolean,
            default: () => false
        },
        beforeClick: {
            type: Function,
            default: () => () => {}
        },
        currentMode: {
            type: String,
            default: "normal"
        },
        // TODO update code
        environment: {
            type: String,
            default: "crm"
        },
        catalog: {
            type: Object,
            default: () => {}
        },
        allCategoryList: {
            type: Array,
            default: (_) => []
        }
    },
    computed: {
        ...mapState(["cardCategoryIdMap", "connectDNCInfo"]),
        minHeightStyle() {
            return "min-height:312px;display: block;width:100%";
        },
        appEmptyText() {
            return this.$t("main.emptyText");
            // return currentMode === "normal"
            //     ? "There aren't any apps in this category"
            //     : "There aren't any apps in this category,please add first.";
        },
        customizeChimeAddon() {
            return basePermission.customizeChimeAddon;
        },
        customizeThirdPartyAddon() {
            return basePermission.customizeThirdPartyAddon;
        },
        canEditType({ customizeChimeAddon, customizeThirdPartyAddon }) {
            let res = [];
            if (customizeChimeAddon) {
                res.push("CHIME");
            }
            if (customizeThirdPartyAddon) {
                res.push("CUSTOM");
            }
            return res;
        },
        showFirstOrderTitle({ catalog }) {
            return catalog.categoryIndex == -1 || catalog.sortIndex == -1;
        },

        displaySortModuleList({ curSortModuleList, currentMode, catalog }) {
            console.log("显示的 侧边栏数据", curSortModuleList, currentMode, catalog);
            const { isCompanyOwner, isOfficeAdmin } = infoData.getUserInfo();
            if (
                (currentMode === "normal" || currentMode === "view") &&
                catalog.sortIndex == -1 &&
                catalog.isSearch &&
                catalog.searchStr
            ) {
                // if category has no card ,hide  ||

                return curSortModuleList.map((sort, idx) => {
                    const newSort = JSON.parse(JSON.stringify(sort));
                    const categoryList = newSort.categoryList.map((cat) => {
                        if (!cat.appList || cat.appList.length === 0) {
                            cat.hidden = true;
                        }

                        if (cat.key === "TeamAddOn" && (isCompanyOwner || isOfficeAdmin)) {
                            cat.hidden = true;
                        }
                        return cat;
                    });
                    const showCate = categoryList.filter((i) => !i.hidden);
                    if (showCate.length === 0) {
                        newSort.hidden = true;
                    }
                    newSort.categoryList = categoryList;
                    return newSort;
                });
            }
            const showCate = curSortModuleList.filter((i) => i.categoryList.length);
            if (showCate.length === 0) {
                return [];
            }
            return curSortModuleList.map((i) => {
                if (i.categoryList.length === 0) {
                    i.hidden = true;
                }
                return i;
            });
        },

        suggestCardList({ allCategoryList }) {
            return allCategoryList?.[1]?.appList || [];
        }
    },
    watch: {
        sortModuleList: {
            handler(val) {
                this.curSortModuleList = JSON.parse(JSON.stringify(val));
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        ...mapMutations(["updateCardCategoryIdMap"]),
        getBtnWrapperClass(item) {
            const cls = ["btn-warpper"];
            const { appKey } = item;

            if (appKey === "DNC") {
                cls.push("not-hover");

                const hasSlot = this.hasAdditionalSlot(item);

                if (hasSlot) {
                    cls.push("flex-left");
                }
            }

            return cls.join(" ");
        },
        hasAdditionalSlot(item) {
            return !!(
                item.appKey === "DNC" &&
                this.currentMode === "normal" &&
                this.connectDNCInfo?.accountName
            );
        },
        // beforeClick click It is an event that needs to be triggered uniformly before triggering the click event
        onClick(item) {
            this.beforeClick(item);
        },
        move(evt) {
            let targetClass = evt.to.parentElement.offsetParent._prevClass;
            let isCanMove = this.canEditType.some((i) => {
                return targetClass.indexOf(i) > -1;
            });

            console.log(evt, 9988);
            const { list } = evt.relatedContext;

            const { element } = evt.draggedContext;

            const match = list.find(
                (l) => l.id === element.id && l.categoryId !== element.categoryId
            );

            return isCanMove && !match;
        },
        canHandle(item) {
            /**
             * type: CUSTOM_APP、CHIME_APP、INTEGRATION_SYSTEM_APP、INTEGRATION_ADMIN_APP
             */
            let havePermission;
            if (item.type == "CHIME_APP") {
                havePermission = this.customizeChimeAddon;
            } else if (
                item.type == "INTEGRATION_ADMIN_APP" ||
                item.type == "INTEGRATION_SYSTEM_APP" ||
                item.type == "CUSTOM_APP"
            ) {
                havePermission = this.customizeThirdPartyAddon;
            }
            return havePermission && this.currentMode === "editing";
        },
        changeHandler(item, flag) {
            //  edit and view  Can't operate
            if (this.currentMode !== "normal") {
                return;
            }
            this.$emit("update", item, flag);
        },
        endHandler(e) {
            const { item, to } = e;
            let cardId = item.getAttribute("id");
            let categoryId = item.getAttribute("categoryid");
            let targetCateId = to.offsetParent.getAttribute("cateid");
            if (cardId && categoryId && targetCateId !== categoryId) {
                cardId = parseInt(cardId);
                categoryId = parseInt(categoryId);
                targetCateId = parseInt(targetCateId);
                // update
                const cloneCardCategoryIdMap = lodash.cloneDeep(this.cardCategoryIdMap);
                const currCategoryIds = (cloneCardCategoryIdMap.get(cardId) || []).filter(
                    (i) => i !== categoryId
                );
                currCategoryIds.push(targetCateId);
                cloneCardCategoryIdMap.set(cardId, currCategoryIds);
                this.updateCardCategoryIdMap(cloneCardCategoryIdMap);
            }
            this.$emit("update-render-list", this.curSortModuleList);
        },
        //  delete
        async delHandler(sortItem, cateItem, appItem) {
            if (!appItem.canHide) {
                return;
            }
            //

            if (appItem.appKey === "leadPond" && this.environment !== "crm") {
                utils.toast({
                    content: this.$t("dataConfig.leadPond.delTip2")
                });
                return;
            }

            if (appItem.appKey === "leadPond") {
                // if subscribed then toast
                const subscribedList = await leadPondApi.fetchSubscribedPkg();
                if (subscribedList.length) {
                    utils.toast({
                        content: this.$t("dataConfig.leadPond.delTip")
                    });
                    return;
                }

                // else cancel
            }

            //  hidden linkage
            const dialerList = ["dialer", "text", "threeLineDialer", "unlimitedText"];

            if (dialerList.indexOf(appItem.appKey) > -1) {
                // TODO LSS
                const { ok } = await popMgr.confirm({
                    desc: this.$t("container.confirmDeleteMsg"),
                    okText: this.$st("common", "yes"),
                    cancelText: this.$st("common", "no"),
                    maxWidth: "440px"
                });
                if (!ok) {
                    return;
                }
            }

            // handler card category map
            const cloneCardCategoryIdMap = lodash.cloneDeep(this.cardCategoryIdMap);
            const currCategoryIds = (cloneCardCategoryIdMap.get(appItem.id) || []).filter(
                (i) => i !== cateItem.id
            );
            cloneCardCategoryIdMap.set(appItem.id, currCategoryIds);
            this.updateCardCategoryIdMap(cloneCardCategoryIdMap);
            this.$emit("hide-card", { sortItem, cateItem, appItem });
        },
        //  edit
        editHandler(item, { sortIdx, cateIdx, appIdx, cateItem }) {
            let categoryIds = [];
            const oldCategoryIds = this.cardCategoryIdMap.get(item.id) || [];
            if (item.type === "CUSTOM_APP") {
                categoryIds = oldCategoryIds;
            }
            //  Adjust the box
            createVendorAppPop({
                config: item,
                isTransferSaveApi: false,
                currCateIds: categoryIds,
                categoryList: this.allCategoryList
            }).then(({ type, data, operate }) => {
                //  only deal with  edit situation
                if (type && operate === "edit") {
                    if (data.type == "CUSTOM_APP" && data.categoryIds.length > 0) {
                        // handler card category map
                        const cloneCardCategoryIdMap = lodash.cloneDeep(this.cardCategoryIdMap);
                        cloneCardCategoryIdMap.set(item.id, data.categoryIds);
                        this.updateCardCategoryIdMap(cloneCardCategoryIdMap);
                        this.$emit("edit-multi-card", {
                            oldCategoryIds,
                            categoryIds: data.categoryIds,
                            cardItem: data,
                            sortIdx,
                            cateIdx
                        });
                    } else {
                        const curSortObj = this.curSortModuleList?.[sortIdx] || {};
                        const curCateObj = curSortObj.categoryList?.[cateIdx] || {};
                        if (!curCateObj.appList || !curCateObj.appList.length) {
                            return;
                        }
                        const hasApp = curCateObj.appList.find((m) => m.id === data[0].id);
                        if (hasApp) {
                            return false;
                        }
                        curCateObj.appList[appIdx] = data;
                        this.$set(
                            this.curSortModuleList[sortIdx].categoryList,
                            cateIdx,
                            curCateObj
                        );
                        this.$emit("update-render-list", this.curSortModuleList);
                    }
                }
            });
        },

        openAppAddPop(sortIdx, cateIdx) {
            const { temporaryHideApps, allCategoryList } = this;
            const currCategory = this.curSortModuleList[sortIdx]?.categoryList?.[cateIdx] || {};
            const currCateIds = currCategory.belong === "CHIME" ? [] : [currCategory.id];
            // 当前category下的app需要过滤。一个category下不能有相同卡片，但是不同category下可以有相同卡片
            const filterApps = currCategory?.appList.map((i) => i.id);
            createAppPop({
                addApps: temporaryHideApps,
                filterApps,
                currCateIds,
                categoryList: allCategoryList,
                fromCategoryId: currCategory.id
            }).then(({ type, data }) => {
                if (!type) {
                    return false;
                }
                //
                const categoryIds = data[0].categoryIds || [];
                if (data[0].type === "CUSTOM_APP" && categoryIds.length > 0) {
                    const cloneCardCategoryIdMap = lodash.cloneDeep(this.cardCategoryIdMap);
                    const oldCategoryIds = cloneCardCategoryIdMap.get(data[0].id) || [];
                    cloneCardCategoryIdMap.set(data[0].id, categoryIds);
                    this.updateCardCategoryIdMap(cloneCardCategoryIdMap);
                    this.$emit("edit-multi-card", {
                        oldCategoryIds,
                        categoryIds,
                        cardItem: data[0]
                    });
                } else {
                    const curSortObj = this.curSortModuleList?.[sortIdx] || {};
                    const curCateObj = curSortObj.categoryList?.[cateIdx] || {};
                    if (!curCateObj.appList || !curCateObj.appList.length) {
                        curCateObj.appList = [];
                    }
                    const hasApp = curCateObj.appList.find((m) => m.id === data[0].id);
                    if (hasApp) {
                        return false;
                    }
                    curCateObj.appList = [...data, ...curCateObj.appList];
                    this.$set(this.curSortModuleList[sortIdx].categoryList, cateIdx, curCateObj);
                    this.$emit("update-render-list", this.curSortModuleList);
                }
            });
        },
        hideIconTip(item) {
            //  if not hidden  ， hint
            return {
                maxWidth: "300px",
                content:
                    !item.canHide && this.currentMode === "editing"
                        ? this.$t("container.iconTip1")
                        : ""
            };
        },
        async additionalClickHandle(item) {
            this.$emit("additionalClick", { data: item });
        }
    },
    mounted() {}
};
</script>

<style scoped lang="less">
.marketplace .marketbody .card-panel {
    position: relative;
    margin-bottom: 20px;
}

.marketplace .marketbody .card-panel > div {
    width: 100%;
}
.marketplace .marketbody .card-panel > div > span {
    // display: block;
    // column-count: 2;
    // column-gap: 20px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
}
.card-group-box {
    div:last-child {
        div:last-child {
            .card-panel span {
                &:not(.icon2017):last-child {
                    margin-bottom: 80px;
                }
            }
        }
    }
}
.sort-title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .app-add-box {
        cursor: pointer;
        color: var(--primary-color);
        background: rgba(var(--primary-color-rgb), 0.1);
        border-radius: 15px;
        font-weight: 400;
        line-height: 20px;
        padding: 5px 10px;
        font-size: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon2017 {
            margin-right: 5px;
        }
        span {
            font-size: 14px;
        }
        &:hover {
            background: rgba(var(--primary-color-rgb), 0.2);
        }
        &:active {
            background: rgba(var(--primary-color-rgb), 0.3);
        }
    }
}
.del-icon {
    text-align: center;
    z-index: 4;
    cursor: pointer;
    background: #fff;
    color: @error-color;
    &:hover {
        background: @error-color;
        color: #fff;
        cursor: pointer;
    }
    &:active {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), @error-color;
    }
}
.drag-icon,
.icon-draw_05 {
    &:hover {
        background: var(--primary-color);
        color: #fff;
        cursor: pointer;
    }
    &:active {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), var(--primary-color);
    }
}
.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    &:hover {
        cursor: pointer;
        background: #fff;
        color: @error-color;
    }
}
</style>
<style lang="less">
.market-place-card-ghost {
    border: 1px dashed #e1e2e6 !important;
    background-size: 100% 100%;
    justify-content: space-between;
    .market-card-box,
    .icon-box,
    .left-top-tag,
    .switch-wrap,
    .del-icon {
        visibility: hidden;
    }
}
.card-group-box {
    position: relative;
    height: 100%;
    & > div {
        margin-bottom: 20px;
    }
    .category-first-order-title {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        margin-bottom: 40px;
        p {
            margin: 0 10px;
            font-weight: 700;
            font-size: 30px;
            line-height: 40px;
            text-align: center;
            color: var(--label-color);
            flex-shrink: 0;
        }
        .slider {
            width: 100%;
            height: 1px;
            background: var(--border-color);
        }
    }
}
.card-group-box .three-columns-box {
    margin-bottom: 80px;
    .market-card,
    .empty-add-box {
        width: calc(33% - 11px);
        &:nth-child(3n + 2) {
            margin-left: 20px;
        }
        &:nth-child(3n) {
            margin-left: 20px;
        }
    }
}
.two-columns-box {
    .market-card,
    .empty-add-box {
        width: calc(50% - 10px);
        &:nth-child(2n) {
            margin-left: 20px;
        }
    }
    .empty-add-box {
        width: calc(50% - 10px) !important;
    }
}
.market-card.market-place-card-chosen.sortable-fallback.sortable-drag {
    position: fixed !important;
}
</style>
