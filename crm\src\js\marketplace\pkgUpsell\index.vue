<template>
    <div class="package-upsell" v-loading="loading">
        <div class="container">
            <h3 class="package-upsell-title">{{ $t("title1") }}</h3>
            <!-- package contaienr start -->
            <div class="packages-container-wrap">
                <div class="packages-container current-package">
                    <div class="pkg-cont-title">
                        {{ $t("text1") }}
                    </div>
                    <div class="pkg-cont-body foundation">
                        <PackageCard
                            class="package-card"
                            :style="{
                                cursor: 'default',
                                width: `${currentPkgWidth}px`
                            }"
                            type="currentPkg"
                            :dataSource="currentPkg"
                        />
                    </div>
                </div>
                <div class="packages-container">
                    <div class="pkg-cont-title">
                        {{ $t("text2") }}
                    </div>
                    <div class="pkg-cont-body">
                        <PackageCard
                            v-for="(pkg, index) in choosePkgs"
                            :key="index"
                            class="package-card"
                            :style="{
                                width: `${pkgWidth}px`
                            }"
                            :dataSource="pkg"
                            type="choosePkg"
                            :isSelected="currentId === pkg.id"
                            @click.native="setCurrentPackage(pkg)"
                        />
                    </div>
                </div>
            </div>
            <!-- package contaienr end -->

            <template v-if="isFreeTrialUpgrade">
                <h3 class="order-title">{{ $t("title2") }}</h3>
                <DataTable :data="orderPkgInfo" showSummary>
                    <template>
                        <DataTableColumn :label="$t('table.name')" :widthRatio="1" :width="340">
                            <p class="ellipsis" :title="firstName">
                                {{ firstName }}
                            </p>
                        </DataTableColumn>
                        <DataTableColumn
                            :label="$t('table.currentPackage')"
                            :widthRatio="1"
                            :minWidth="466"
                            :width="500"
                        >
                            <p class="ellipsis flex-container">
                                <span class="curpackage">{{ $t("text3") }}</span>
                            </p>
                        </DataTableColumn>
                        <DataTableColumn
                            :label="$t('table.newPackage')"
                            :widthRatio="1"
                            :width="340"
                        >
                            <p
                                class="ellipsis"
                                :title="(choosePkgs[0] && choosePkgs[0]['name']) || ''"
                            >
                                {{ (choosePkgs[0] && choosePkgs[0]["name"]) || "" }}
                            </p>
                        </DataTableColumn>
                    </template>
                    <template v-slot:footer>
                        <div
                            class="order-info-line border-bottom"
                            v-html="
                                $t('text4', {
                                    price: orderInfo.upgradeFee,
                                    time: `${orderInfo.firstMonthStartTime} - ${orderInfo.firstMonthEndTime}`
                                })
                            "
                        ></div>
                        <div
                            class="order-info-line"
                            v-html="
                                $t('text5', {
                                    price: orderInfo.nextMonthFee,
                                    time: orderInfo.nextMonthStartTime
                                })
                            "
                        ></div>
                    </template>
                </DataTable>
            </template>
            <!-- payment info start -->
            <PaymentInfo
                class="package-payment"
                :canAddAnotherCard="!isNewObDashboard"
                ref="payment"
                :bindCardText="bindCardText"
                :cardList="creatorCardList"
                :needBindCard="noCard"
                :validateResult.sync="formValid"
                :isUpdatePayCard.sync="isUpdatePayCard"
                @input="handleNewCard"
                @changePaymentType="changePaymentType"
                @paymentInfoUpdated="calcTax"
                :isShowAch="true"
                @setCard="setNewCard"
            >
                <template v-slot:addCardNewAgreeTip>
                    <div class="agreement">
                        <CheckBox :checked="isAgree" @change="({ checked }) => (isAgree = checked)">
                            <template #label>
                                <span class="read-label" v-html="$t('text6')"> </span>
                            </template>
                        </CheckBox>
                    </div>
                </template>
                <template v-slot:cardTip>
                    <div class="fu-card-tip-container">
                        <div class="pay-tip" v-html="$t('text8')"></div>
                    </div>
                </template>
            </PaymentInfo>

            <!-- payment info end -->
        </div>
        <!-- footer start -->
        <ChargeInfo
            class="charge-info"
            :showTax="showTax"
            taxIconStyle="left:130px"
            chargeText="Today's Charge:"
            :stepNum="1"
            :step="2"
        >
            <template v-slot:chargeText>
                <div>
                    <p class="pay-charge">
                        <span class="pay-word">{{ $t("text9") }}</span>
                        <span class="moeny">{{
                            $st("common", "price2", {
                                count: formatToTwoDecimalsLocale(displayedTotalCharge)
                            })
                        }}</span>
                        <DropDown
                            v-if="showTax"
                            class="charge-drop"
                            :noBorder="true"
                            :clickMode="true"
                            placement="top"
                            dropdownCls="tax-drop-cls"
                            style="display: inline-block"
                        >
                            <template #body="data">
                                <div class="tax-detail-btn flex-row">
                                    <span>{{ $st("common", "chargeInfo.taxDetail") }}</span>
                                    <span
                                        class="tax-detail-btn-icon icon2017 icon-arrow_01_down"
                                        :class="{
                                            'icon-arrow_01_up': data.isOpen
                                        }"
                                    ></span>
                                </div>
                            </template>
                            <template #dropdown>
                                <div class="charge-info">
                                    <span class="tip-arrow"></span>
                                    <div class="charge-detail">
                                        <p>
                                            <span>{{ $st("common", "pay.chargeDes") }}</span
                                            ><span
                                                >{{ currencySymbol
                                                }}{{ formatToTwoDecimalsLocale(toDayCharge) }}</span
                                            >
                                        </p>
                                        <p>
                                            <span>{{ $st("common", "chargeInfo.totalTaxes") }}</span
                                            ><span
                                                >{{ currencySymbol
                                                }}{{ formatToTwoDecimalsLocale(totalTax) }}</span
                                            >
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </DropDown>
                    </p>
                    <div class="pay-msg" v-if="!isFreeTrialUpgrade">
                        {{ $t("text10") }}
                    </div>
                </div>
            </template>
            <template v-slot:policy>
                <CheckBox
                    :checked="isRead"
                    @change="({ checked }) => (isRead = checked)"
                ></CheckBox>
                <span
                    class="pay-agree"
                    v-click="{
                        selector: '.tu-link',
                        callback: readTeamOfUse
                    }"
                    v-html="$t('text7')"
                >
                </span>
            </template>
            <template v-slot:btn>
                <button
                    type="button"
                    class="chime-btn primary"
                    @click="order"
                    :disabled="btnDisabled"
                >
                    {{ $t("text11") }}
                </button>
            </template>
        </ChargeInfo>
        <!-- footer end -->
    </div>
</template>
<script>
import PackageCard from "./components/packageCard.vue";
import http from "./api/index.js";
import { PaymentInfo, ChargeInfo, crmUtils, infoData, track, payTipPop, globalApi } from "crm";
import TermOfUsePop from "@/js/common-module/termOfUsePop/index.js";
import { components, popMgr, utils } from "common";
import popVue from "./pop/pkgUpsellSuccessPop";
const { CheckBox, DataTable, DropDown } = components;
import useTax from "@/js/common-module/hooks/useTax.js";
import { useCurrencySymbol } from "@/hooks";

export default {
    langModule: "pkgUpsell",
    name: "PkgUpsell",
    components: { PackageCard, PaymentInfo, ChargeInfo, CheckBox, DataTable, DropDown },
    setup() {
        const { showTax, taxInfo, openDetailBefore } = useTax();
        const currencySymbol = useCurrencySymbol();
        return { showTax, taxInfo, openDetailBefore, currencySymbol };
    },
    data() {
        const { packageName, isSingleAgent, firstName } = infoData.getUserInfo();
        return {
            packages: [],
            choosePkgs: [],
            isSingleAgent,
            packageName,
            firstName,
            currentId: 15,
            creatorCardList: [],
            isRead: false,
            formValid: false,
            loading: false,
            paied: false,
            selectPkg: {},
            toDayCharge: "0.00",
            orderPkgInfo: [{}],
            orderInfo: {},
            paymentInfo: {},
            isAgree: true,
            paymentType: "creditCard",
            isUpdatePayCard: false,
            newPayCardId: null,
            newCard: {}
        };
    },
    watch: {
        toDayCharge: {
            handler: function () {
                this.calcTax();
            }
        }
    },
    computed: {
        totalTax({ taxInfo }) {
            return taxInfo?.totalTax ?? 0;
        },
        displayedTotalCharge({ toDayCharge, totalTax }) {
            return utils.add([toDayCharge, totalTax]);
        },
        isNewObDashboard() {
            return infoData.isNewObDashboard;
        },
        bindCardText({ $t, isFreeTrialUpgrade }) {
            return {
                desc: isFreeTrialUpgrade ? $t("descripe1") : $t("descripe8"),
                notes: [$t("descripe2")]
            };
        },
        //  Is it  new enterprise of  company instance
        isCompanyInstance() {
            const { teamInstance } = infoData.getUserInfo();
            return teamInstance === 1;
        },
        computedTeamName(isCalitalLized = true) {
            const { teamRole, isOfficeAdmin } = infoData.getUserInfo();
            let _name = isCalitalLized ? "Team" : "team";
            if (this.isCompanyInstance) {
                if (isOfficeAdmin) {
                    _name = isCalitalLized ? "Group" : "group";
                }
                if (teamRole < 2) {
                    _name = isCalitalLized ? "Company" : "company";
                }
            }
            return _name;
        },
        noCard() {
            return this.creatorCardList.length == 0;
        },
        btnDisabled() {
            return !this.formValid || this.paied || !this.isAgree;
        },
        currentPkg() {
            let pkgId = infoData.getUserInfo().paymentInfo?.id || this.paymentInfo?.id;
            return this.packages.find((i) => i.id === pkgId);
        },
        isCreator() {
            return infoData.getUserInfo().isTeamOwner;
        },
        isFreeTrialUpgrade() {
            const { isFreeTrial } = infoData.getUserInfo();
            return isFreeTrial && this.$route.query.packageId;
        },
        pkgWidth() {
            return this.choosePkgs.length == 3 ? "270" : "360";
        },
        currentPkgWidth() {
            return this.choosePkgs.length == 3 ? "280" : "360";
        }
    },
    mounted() {
        this.initData();
    },
    methods: {
        async initData() {
            const { packageId = "" } = this.$route.query;

            // scroll to top
            $("html")[0].scrollTop = 0;
            //  Obtain card binding information
            http.getCardInfo().then((res) => {
                this.creatorCardList = res.data.bankCardList;
                this.creatorCardList.length && (this.formValid = true);
            });
            if (this.isFreeTrialUpgrade) {
                this.initFreeTrial();
            }
            this.initPaymentInfo();
            //  Get the current optional package data ， and total package data
            const { allPackages = [], optionalPackage = [] } = await Promise.all([
                http
                    .getPackages()
                    .then((res) => res)
                    .catch(() => {
                        return [];
                    }),
                http
                    .getSolutionsConfig()
                    .then((res) => res)
                    .catch(() => {
                        return {};
                    })
            ]).then(([r1, r2]) => {
                const { data: { solutions = [] } = {} } = r2;
                let optionalPackage = [];

                solutions.forEach((v) => {
                    optionalPackage = optionalPackage.concat(v.packages);
                });
                return { allPackages: r1, ...r2, optionalPackage };
            });

            this.packages = allPackages;
            this.choosePkgs = packageId
                ? optionalPackage.filter((v) => v.id == packageId)
                : optionalPackage;
            this.currentId = this.choosePkgs[0].id;
            this.selectPkg = this.choosePkgs[0];
        },
        calcTax() {
            this.openDetailBefore({
                paymentRef: this.$refs.payment,
                taxItems: [
                    {
                        productServiceId: "main_package",
                        amount: Number(this.toDayCharge) || 0,
                        quantity: 1
                    }
                ]
            });
        },
        formatToTwoDecimalsLocale: utils.formatToTwoDecimalsLocale,
        initFreeTrial() {
            //  Get package upgrade deduction information
            http.getFreeTrialUpgradeFee(this.$route.query.packageId)
                .then((response) => {
                    const {
                        data: {
                            upgradeFee,
                            firstMonthStartTime,
                            nextMonthStartTime,
                            nextMonthFee,
                            firstMonthEndTime
                        } = {}
                    } = response;
                    if (upgradeFee) {
                        this.toDayCharge = upgradeFee;
                    }
                    this.orderInfo = {
                        firstMonthStartTime,
                        nextMonthStartTime,
                        nextMonthFee,
                        upgradeFee,
                        firstMonthEndTime
                    };
                })
                .catch((error) => {
                    console.error("getFreeTrialUpgradeFeeError", error);
                });
        },
        /**
         *  Expired Package Acquisition paymentInfo
         */
        initPaymentInfo() {
            if (!infoData.getUserInfo().paymentInfo) {
                globalApi.getSelfPaymentInfo().then((res) => {
                    if (res) {
                        this.paymentInfo = res;
                    }
                });
            }
        },
        setCurrentPackage(pkg) {
            this.selectPkg = pkg;
            this.currentId = pkg.id;
        },
        handleNewCard(newCard) {
            console.log(newCard);
        },
        setMyCard() {
            var card = {
                cardType: this.newCard.cardType,
                holderName: this.newCard.holderName,
                cardNumber:
                    this.newCard.cardNumber.length > 12
                        ? this.newCard.cardNumber.slice(12)
                        : this.newCard.cardNumber,
                cardOwnerType: this.isCreator ? "creator" : "member"
            };
            this.creatorCardList = [card];
        },
        setNewCard(card) {
            this.newCard = card;
            this.setMyCard();
        },
        plaidSuccess(data) {
            data.holderName = data.cardHolderName;
            this.creatorCardList = [data];
            this.formValid = true;
        },
        changePaymentType(type) {
            this.paymentType = type;
        },
        async readTeamOfUse(isClickTOULink = true) {
            let param = {
                showAgree: !isClickTOULink
            };
            if (isClickTOULink || !this.isRead) {
                const res = await TermOfUsePop.createTermOfUsePop(param);
                if (!isClickTOULink && !this.isRead) {
                    this.isRead = res.flag;
                    return res.flag;
                }
            }
            return true;
        },
        goAgentSettting() {
            window.__crmRouter.push({
                name: "setting_agent"
            });
        },
        async order() {
            //  If you do not currently have a bank card ， Bind the card first ， repay
            const isAgree = await this.readTeamOfUse(false);
            if (!isAgree) {
                return;
            }
            this.loading = true;

            let cardPromise = Promise.resolve(0);
            if (this.$refs.payment) {
                let { needBindCard, newBindCardId } = this.$refs.payment.getNewBindCard();
                cardPromise = needBindCard
                    ? this.$refs.payment.bindCard().then((d) => d.id)
                    : Promise.resolve(newBindCardId);
            }

            const [_bindErr, bindData] = await crmUtils.awaitWrap(cardPromise);
            if (bindData) {
                const [_queryErr, queryData] = await crmUtils.awaitWrap(http.getCardInfo());
                if (queryData?.data?.bankCardList) {
                    this.creatorCardList = queryData.data.bankCardList;
                    this.creatorCardList.length && (this.formValid = true);
                }
            }
            this.loading = false;
            //  if there is a card ， Direct payments ， Pop-ups
            const { userId } = infoData.getUserInfo();
            const packageName =
                this.selectPkg?.type?.charAt(0)?.toUpperCase() + this.selectPkg?.type?.slice(1);
            //  in the case of freeTrial new window came in
            if (this.isFreeTrialUpgrade) {
                const [err, { data: { payResult } = {} } = {}] = await crmUtils.awaitWrap(
                    http.upGradePackage(
                        userId,
                        this.$route.query.packageId,
                        bindData ? { payMethodId: bindData } : {}
                    )
                );
                if (err || (payResult && !payResult.payResult)) {
                    utils.toast({
                        content: this.$t("toast.text1")
                    });
                    this.loading = false;
                    track.trackGa.sendEvent({
                        eventCategory: "CRM_Freetrial_Subscribe",
                        eventAction: "Pay fail",
                        eventLabel: `PkgId:${this.$route.query.packageId}, name:${packageName}`
                    });
                    return;
                }
                track.trackGa.sendEvent({
                    eventCategory: "CRM_Freetrial_Subscribe",
                    eventAction: "Pay success",
                    eventLabel: `PkgId:${this.$route.query.packageId}, name:${packageName}`
                });
                popMgr
                    .create(popVue, {
                        ok: () => {
                            //  Upgrade package interface
                            window.opener.postMessage("freeTrialUpgradeSuccess/" + packageName);
                            window.close();
                        },
                        close: () => {
                            //  Upgrade package interface
                            window.opener.postMessage("freeTrialUpgradeSuccess/" + packageName);
                            window.close();
                        }
                    })
                    .init({
                        type: packageName
                    });
                return;
            }

            let params = {
                crmPackageId: this.currentId
            };

            if (bindData) {
                params.payMethodId = bindData;
            }

            const [_billErr, billData] = await crmUtils.awaitWrap(http.payBill(params));
            if (billData?.data) {
                if (billData.data?.errorCode == 1010005) {
                    payTipPop.createTipPop({
                        type: "fail",
                        typeDes: this.$st("common", "paymentInfo.errorMsg.err4"),
                        autoClose: 5000
                    });
                    return;
                }

                if (this.creatorCardList[0]?.cardType === "ACH") {
                    payTipPop.createTipPop({
                        type: "active",
                        typeDes: this.$st("common", "paymentInfo.successMsg.achPaySuccess"),
                        autoClose: 2000
                    });
                }

                track.trackGa.sendEvent({
                    eventCategory: "CRM_PayBill",
                    eventAction: "Pay success",
                    eventLabel: `PkgId:${this.currentId}, name:${packageName}`
                });
                //  renew infoData
                await infoData.initUserInfo();
                await infoData.initPackageInfo();
                //  Payment Status Update
                this.paied = true;
                popMgr
                    .create(popVue, {
                        ok: () => {
                            this.goAgentSettting();
                        },
                        close: () => {
                            this.goAgentSettting();
                        }
                    })
                    .init({
                        type: packageName
                    });
            } else {
                utils.toast({
                    content: this.$t("toast.text1")
                });
                track.trackGa.sendEvent({
                    eventCategory: "CRM_PayBill",
                    eventAction: "Pay fail",
                    eventLabel: `PkgId:${this.currentId}, name:${packageName}`
                });
            }
            this.loading = false;
        }
    }
};
</script>
<style lang="less">
.package-upsell {
    padding-top: 20px;
    padding-bottom: 150px;
    font-family: "SF UI Text";
    background-color: #f6f7fb;

    .package-payment {
        margin-top: 40px;
        h3 {
            font-size: 18px;
            line-height: 2;
            letter-spacing: normal;
            color: #202437;
            .desc {
                font-size: 14px;
                color: #797e8b;
                font-weight: normal;
                margin-bottom: 10px;
            }
        }
    }
    .fu-card-tip-container {
        display: flex;
        margin-top: 20px;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.29;
        letter-spacing: normal;
        color: #797e8b;
        .policy {
            display: flex;
            align-items: center;
        }
        a {
            cursor: pointer;
            text-decoration: underline;
            color: var(--primary-color);
        }
        .pay-tip {
            margin-top: 0;
            margin-left: auto;
        }
    }
    .container {
        width: 1200px;
        margin: 0 auto;

        .package-upsell-title {
            margin-bottom: 20px;
            font-size: 24px;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            color: #202437;
        }
        .packages-container-wrap {
            display: flex;
            flex-direction: row;
            .packages-container {
                border-radius: 4px;
                border: 1px solid #e1e2e6;
                background-color: #ffffff;
                display: flex;
                flex-direction: column;
                flex: 1;
                &.current-package {
                    flex: 1;
                    margin-right: 20px;
                }
                .pkg-cont-title {
                    padding: 15px 20px;
                    border-bottom: 1px solid #ebecf1;
                    font-size: 16px;
                    font-weight: 600;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.25;
                    letter-spacing: normal;
                    color: #515666;
                }
                .pkg-cont-body {
                    flex-grow: 1;
                    padding: 20px;
                    display: flex;
                    justify-content: center;
                    flex-direction: row;
                    &.foundation {
                        flex-direction: column;
                        align-items: center;
                        .package-card {
                            flex-grow: 1;
                        }
                    }
                    .package-card {
                        margin-right: 20px;
                        &:nth-last-of-type(1) {
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
    .charge-info {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        .charge-drop {
            margin-left: 10px;
        }
        .pay-msg {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 400;
            color: #797e8b;
        }
    }
    h3.order-title {
        font-size: 18px;
        line-height: 2;
        margin-top: 40px;
        margin-bottom: 10px;
    }
    .com-datatable {
        .com-datatable-body {
            min-height: 50px;
        }
        .order-info-line {
            height: 50px;
            text-align: right;
            line-height: 50px;
            padding-right: 30px;
            color: #202437;
            font-weight: bold;
            &.border-bottom {
                border-bottom: 1px solid #ebecf1;
            }
            .fee {
                font-weight: 600;
                color: var(--primary-color);
            }
        }
    }
}
.agreement {
    margin-top: 20px;
    font-size: 14px;
    line-height: 18px;
    color: #797e8b;
    .link {
        color: var(--primary-color);
        text-decoration: underline;
    }
}
</style>
