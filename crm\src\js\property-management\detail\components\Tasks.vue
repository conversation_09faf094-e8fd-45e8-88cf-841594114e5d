<template>
  <div class="tasks">
    <div class="title-bar">
      <h2>Tasks</h2>
      <div class="right">
        <div class="icon-area"><img src="https://via.placeholder.com/150" alt="settings icon" /></div>
        <div class="icon-area"><img src="https://via.placeholder.com/150" alt="add icon" /></div>
      </div>
    </div>
    <div class="lists">
      <div class="list-item" v-for="(task, index) in tasks" :key="index">
        <div class="check-area">
          <input type="checkbox" :checked="task.completed" />
        </div>
        <div class="right">
          <div class="text">
            <p :class="{ completed: task.completed }">{{ task.title }}</p>
            <div class="tag" v-if="task.tag">{{ task.tag }}</div>
          </div>
          <div class="subtext" v-if="task.subtext">
            <a href="#">{{ task.subtext.text }} <img :src="task.subtext.icon" alt="arrow icon" /></a>
          </div>
          <div class="tags" v-if="task.tags">
            <div class="tag-item" v-for="(tag, i) in task.tags" :key="i">
              <img :src="tag.icon" :alt="tag.alt" />
              <span>{{ tag.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Tasks',
  data() {
    return {
      tasks: [
        {
          completed: false,
          title: 'AML Check On Vendors',
          tag: 'System',
        },
        {
          completed: false,
          title: 'Send property details to owner for Approval',
          tag: 'System',
          subtext: {
            text: 'View Feedback',
            icon: 'https://via.placeholder.com/150',
          },
        },
        {
          completed: false,
          title: 'Upload EPC',
          tag: 'System',
          subtext: {
            text: 'Upload EPC',
            icon: 'https://via.placeholder.com/150',
          },
        },
        {
          completed: false,
          title: 'Upload digital assests',
          tag: 'System',
          subtext: {
            text: 'Upload',
            icon: 'https://via.placeholder.com/150',
          },
        },
        {
          completed: false,
          title: 'Configure viewing instruction',
          tag: 'System',
          subtext: {
            text: 'Configure',
            icon: 'https://via.placeholder.com/150',
          },
        },
        {
          completed: false,
          title: 'This is task related with lead',
          tags: [
            { icon: 'https://via.placeholder.com/150', alt: 'people icon', text: 'John Smith' },
            { icon: 'https://via.placeholder.com/150', alt: 'people source icon', text: 'Agent A' },
            { icon: 'https://via.placeholder.com/150', alt: 'time icon', text: '7th Jul, 2025 09:00' },
          ],
        },
        {
          completed: false,
          title: 'This is task related with some leads',
          tags: [
            { icon: 'https://via.placeholder.com/150', alt: 'people icon', text: 'John Smith' },
            { icon: 'https://via.placeholder.com/150', alt: 'people source icon', text: 'Agent A' },
            { icon: 'https://via.placeholder.com/150', alt: 'time icon', text: '7th Jul, 2025 09:00' },
          ],
        },
        {
          completed: true,
          title: 'This is task without lead',
          tags: [
            { icon: 'https://via.placeholder.com/150', alt: 'people icon', text: 'John Smith' },
            { icon: 'https://via.placeholder.com/150', alt: 'people source icon', text: 'Agent A' },
            { icon: 'https://via.placeholder.com/150', alt: 'time icon', text: '7th Jul, 2025 09:00' },
          ],
        },
      ],
    };
  },
};
</script>

<style scoped>
.tasks {
  border: 1px solid #ebecf1;
  border-radius: 6px;
}
.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebecf1;
}
.title-bar h2 {
  font-size: 16px;
  font-weight: 510;
  color: #202437;
}
.title-bar .right {
  display: flex;
  gap: 10px;
}
.icon-area {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.lists {
  padding: 0 20px 5px;
}
.list-item {
  display: flex;
  gap: 10px;
  padding: 15px 0;
  border-bottom: 1px solid #ebecf1;
}
.list-item:last-child {
  border-bottom: none;
}
.check-area input {
  width: 16px;
  height: 16px;
}
.right {
  width: 100%;
}
.text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}
.text p {
  font-size: 14px;
  font-weight: 510;
  color: #202437;
}
.text p.completed {
  color: #a0a3af;
  text-decoration: line-through;
}
.tag {
  padding: 0 6px;
  height: 20px;
  display: flex;
  align-items: center;
  border-radius: 6px;
  font-size: 12px;
  background-color: rgba(160, 163, 175, 0.1);
  color: #797e8b;
}
.subtext a {
  font-size: 12px;
  color: #5d51e2;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
}
.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 6px;
}
.tag-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 0 6px;
  height: 20px;
  border-radius: 6px;
  background-color: #f6f7fb;
  font-size: 12px;
  color: #797e8b;
}
</style>