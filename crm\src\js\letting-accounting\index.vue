<template>
    <div class="letting-accounting-container">
        <iframe
            ref="lettingFrame"
            :src="iframeSrc"
            frameborder="0"
            width="100%"
            height="100%"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
            @load="onIframeLoad"
        ></iframe>
    </div>
</template>

<script>
import { infoData } from "crm";
export default {
    name: "LettingAccounting",
    data() {
        const env = infoData.userInfo.env;
        const baseUrl =
            env === "production"
                ? "https://lettings.loftyworks.com"
                : "https://lettings.stage.eu.loftyworks.com";
        return {
            iframeSrc: `${baseUrl}/lettings-accounting.html`,
            loading: true
        };
    },
    methods: {
        onIframeLoad() {
            this.loading = false;
            console.log("Lettings application loaded successfully");
        }
    }
};
</script>

<style scoped>
.letting-accounting-container {
    width: 100%;
    height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
    position: relative;
}

iframe {
    border: none;
    width: 100%;
    height: 100%;
}
</style>
