<template>
    <div class="marketplace-catalog">
        <p class="all" :class="{ active: showAll }">
            <span @click="switchCategory()"
                ><span class="icon2017 icon-all_01"></span>{{ $t("category.all") }}</span
            >
            <span class="icon-box" @click="editCategory" v-if="currentMode === 'editing'">
                <span class="icon2017 icon-draw_06"></span>
                <span> {{ $t("category.edit") }}</span>
            </span>
        </p>
        <template v-if="list.length">
            <ul
                class="category-item-box"
                v-for="(catalogObj, sortIndex) in displayList"
                :key="catalogObj.id"
            >
                <li
                    class="item title"
                    :class="{
                        active: active.sortIndex == sortIndex && active.categoryIndex === -1
                    }"
                    v-tip="{ content: catalogObj.title }"
                    @click="
                        switchCategory({
                            sortIndex,
                            sortId: catalogObj.id
                        })
                    "
                >
                    <span class="title-box">
                        <span class="icon2017" :class="[catalogObj.iconCls]"> </span
                        >{{ catalogObj.title }}
                    </span>
                    <div
                        class="icon-arrow-box"
                        @click.stop="catalogObj.expand = !catalogObj.expand"
                    >
                        <span
                            class="icon2017"
                            :class="[catalogObj.expand ? 'icon-arrow_01_up' : 'icon-arrow_01_down']"
                        ></span>
                    </div>
                </li>
                <div v-show="catalogObj.expand">
                    <li
                        class="item"
                        v-for="(item, categoryIndex) in catalogObj.categoryList"
                        :key="item.id"
                        :class="{
                            active:
                                active.sortIndex == sortIndex &&
                                active.categoryIndex == categoryIndex
                        }"
                        @click="
                            switchCategory({
                                sortIndex,
                                categoryIndex,
                                categoryKey: item.categoryKey,
                                sortId: catalogObj.id,
                                categoryId: item.id
                            })
                        "
                    >
                        {{ item.title }}
                        <div v-if="item.showNew" class="new-red">
                            {{ $t("category.new") }}
                        </div>
                    </li>
                </div>
            </ul>
        </template>
        <div class="sort-empty" v-if="!isLoading && list.length <= 0">
            <i class="icon2017 icon-doc"></i>
            <span>{{ $t("category.notAvailable") }}</span>
        </div>
    </div>
</template>
<script>
import { createCategorySettingPop } from "../modules/index.js";

export default {
    langModule: "marketPlace",
    props: {
        list: {
            type: Array,
            default: () => {
                return [];
            }
        },
        active: {
            type: Object,
            default: () => ({
                sortIndex: -1,
                categoryIndex: -1,
                categoryKey: "",
                sortId: "",
                categoryId: ""
            })
        },
        //  current page status  -- normal edit
        currentMode: {
            type: String,
            default: "normal"
        },
        isLoading: {
            type: Boolean,
            default: () => false
        }
    },
    computed: {
        showAll({ active }) {
            return active.sortIndex === -1 && active.categoryIndex === -1;
        }
    },
    data() {
        return {
            displayList: []
        };
    },
    watch: {
        list: {
            handler(val) {
                const iconList = ["icon-dce_shape", "icon-puzzle_04"];
                this.displayList = (val || []).map((i, idx) => {
                    return {
                        expand: true,
                        iconCls: iconList[idx],
                        ...i
                    };
                });
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        switchCategory({
            sortIndex = -1,
            categoryIndex = -1,
            categoryKey = "",
            sortId = "",
            categoryId = ""
        } = {}) {
            this.$emit("change", {
                sortIndex,
                categoryIndex,
                categoryKey: categoryKey || "",
                sortId,
                categoryId
            });
        },
        //  edit category
        editCategory() {
            const { list } = this;
            createCategorySettingPop({
                data: JSON.parse(JSON.stringify(list))
            }).then(({ type, data = {} }) => {
                type && this.$emit("updateCategory", data?.sortList || []);
            });
        }
    }
};
</script>
<style lang="less" scoped>
.marketplace-catalog {
    min-width: 280px;
    margin-right: 30px;
    flex-shrink: 0;
    .title {
        font-size: 18px;
        font-weight: 600;
        line-height: 2;
        color: var(--label-color);
        // padding-left: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }
    .all {
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        color: var(--label-color);
        // padding-left: 10px;
        margin-top: 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        .icon-all_01 {
            margin-right: 10px;
        }
        .icon-box {
            cursor: pointer;
            color: var(--primary-color);
            background: rgba(var(--primary-color-rgb), 0.1);
            border-radius: 15px;
            font-weight: 400;
            line-height: 20px;
            padding: 5px 10px;
            font-size: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            .icon2017 {
                margin-right: 5px;
            }
            span {
                font-size: 14px;
            }
            &:hover {
                background: rgba(var(--primary-color-rgb), 0.2);
            }
            &:active {
                background: rgba(var(--primary-color-rgb), 0.3);
            }
        }
    }

    .category-item-box {
        width: calc(100% - 10px);
    }

    li {
        font-size: 14px;
        line-height: 36px;
        color: #515666;
        // padding-left: 10px;
        position: relative;
        // font-weight: 700;
        &.title {
            font-weight: 700;
            font-size: 16px;
            color: #202437;
            font-weight: 700;
            line-height: 40px;
            margin-top: 10px;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        &:not(.title) {
            padding-left: 26px;
        }

        .title-box {
            width: calc(100% - 30px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .icon2017 {
                margin-right: 10px;
            }
        }

        .icon-arrow-box {
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            .icon2017 {
                color: #a0a3af;
            }
        }

        &.active {
            color: var(--primary-color);
            .icon-arrow-box .icon2017 {
                color: var(--primary-color);
            }
        }
    }
    .new-red {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 10px;
        color: #fff;
        font-weight: 600;
        font-size: 12px;
        width: 40px;
        height: 20px;
        border-radius: 10px;
        line-height: 20px;
        text-align: center;
        background-color: #f0454c;
    }
    li,
    .li {
        &:not(.title) {
            cursor: pointer;
            &:hover {
                color: var(--primary-color);
            }
        }
    }
    .title {
        &:hover {
            color: var(--primary-color);
            .icon2017 {
                color: var(--primary-color);
            }
        }
    }

    .active {
        color: var(--primary-color);
    }
    .sort-empty {
        min-height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        i {
            font-size: 30px;
            color: #c6c8d1;
        }
        span {
            color: #a0a3af;
            font-size: 12px;
            margin-top: 10px;
        }
    }
}
</style>
