<template>
    <div class="package-upsell-card" :class="cls">
        <div class="check-icon">
            <img src="https://static.chimeroi.com/crm/images/marketplace/pkgUpsellIco.svg" />
        </div>
        <div v-if="dataSource.name" class="package-name">
            {{ dataSource.name }}
        </div>
        <div v-if="dataSource.currentFee && !isCurrentPkg" class="package-cur-fee">
            {{ $t("text12", { value: dataSource.currentFee }) }}
        </div>
        <div v-if="dataSource.originFee && !isCurrentPkg" class="package-origin-fee">
            {{ $t("text12", { value: dataSource.originFee }) }}
        </div>
        <div
            v-if="dataSource.ext.stars && dataSource.ext.stars.length && !isCurrentPkg"
            class="package-stars"
        >
            <div
                class="package-star-item"
                v-for="(item, index) of dataSource.ext.stars"
                :key="`${dataSource.name}-star-${index}`"
            >
                <span class="icon2017 icon-collection_02"></span>
                {{ item.value }}
            </div>
        </div>
        <div v-if="dataSource.featureTitle" class="feature-title">
            {{ dataSource.featureTitle }}
        </div>
        <div v-if="dataSource.features && dataSource.features.length" class="package-features">
            <div
                class="package-feature-item"
                v-for="(item, index) of dataSource.features"
                :key="`${dataSource.name}-feature-${index}`"
            >
                <b v-if="item.style === 'bold'">{{ item.value }} </b>
                <span class="normal" v-else>{{ item.value }}</span>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    langModule: "pkgUpsell",
    name: "PackageCard",
    props: {
        dataSource: {
            type: Object,
            default: function () {
                return {
                    id: 0,
                    name: "",
                    type: "",
                    currentFee: undefined,
                    originFee: undefined,
                    ext: {},
                    features: null
                };
            }
        },
        //  Whether the card is selected ， default false
        isSelected: {
            type: Boolean,
            default: false
        },
        //  Card current type ：currentPkg、choosePkg
        type: {
            type: String,
            default: "currentPkg"
        }
    },
    computed: {
        cls() {
            return {
                [this.dataSource.type]: !!this.dataSource.type && !this.isCurrentPkg,
                selected: this.isSelected,
                current: this.isCurrentPkg
            };
        },
        isCurrentPkg() {
            return this.type === "currentPkg";
        }
    }
};
</script>
<style lang="less">
.package-upsell-card {
    --clear-blue: var(--primary-color);
    --background-color: #fafbfd;
    --border-color: #cfdbe7;
    --title-color: #202437;
    --ori-fee-color: #a0a3af;
    --normal-color: #515666;
    --bold-color: #ffffff;
    --line-color: #e4eafb;
    display: flex;
    flex-direction: column;
    font-family: "SF UI Text";
    position: relative;
    box-sizing: border-box;
    width: 360px;
    border-radius: 12px;
    overflow: hidden;
    background-color: var(--background-color);
    &::before {
        content: "";
        display: block;
        height: 4px;
        background-color: var(--border-color);
    }
    cursor: pointer;
    &.selected {
        --bold-color: #fff;
        --title-color: #fff;
        .check-icon {
            display: block;
            line-height: 50px;
            font-size: 18px;
        }
        &.team,
        &.lite,
        &.starter,
        &.core,
        &.company,
        &.enterprise,
        &.premier,
        &.Lofty {
            --header-color: #fff;
            --bold-color: #fff;
            --normal-color: #fff;
            --ori-fee-color: #fff;
            --border-color: rgba(0, 0, 0, 0);
            box-shadow: 0 10px 15px 0 rgba(var(--primary-color-rgb), 0.3);
            background-image: linear-gradient(215deg, var(--primary-color-5), var(--clear-blue));
            .check-icon {
                color: var(--primary-color);
            }
            .package-features {
                background: no-repeat right 0 bottom 0
                    url("https://cdn.chime.me/image/fs/sitebuild/2021322/3/original_f8f851af-a2cf-43b2-aa12-a842bb0bb00a.png");
                background-size: contain;
                .package-feature-item {
                    background-color: rgba(255, 255, 255, 0.1);
                    &::before {
                        opacity: 1;
                        background-color: #fff !important;
                    }
                }
            }
        }
        &.business {
            --header-color: #fff;
            --bold-color: #fff;
            --normal-color: #fff;
            --ori-fee-color: #fff;
            --border-color: rgba(0, 0, 0, 0);
            box-shadow: 0 10px 15px 0 rgba(32, 196, 114, 0.3);
            background-image: linear-gradient(215deg, #43e4ab, #20c472);
            .check-icon {
                color: #20c472;
            }
            .package-features {
                background: no-repeat right 0 bottom 0
                    url("https://cdn.chime.me/image/fs/sitebuild/2021322/3/original_1e3599ed-dd87-43f8-b98c-32973fd999a1.png");
                background-size: contain;
                .package-feature-item {
                    background-color: rgba(255, 255, 255, 0.1);
                    &::before {
                        background-color: #20c472 !important;
                    }
                }
            }
        }
    }
    &.current {
        .package-features {
            .package-feature-item {
                background-color: rgba(255, 255, 255, 0);
                &::before {
                    opacity: 1;
                }
                b {
                    font-weight: normal;
                    --bold-color: var(--normal-color);
                }
            }
        }
    }
    &.team,
    &.lite,
    &.starter,
    &.core,
    &.premier,
    &.enterprise,
    &.freetrial {
        --background-color: var(--primary-color-10);
        --bold-color: var(--primary-color);
        --border-color: var(--primary-color);
        .package-features {
            .package-feature-item {
                background-color: rgba(var(--primary-color-rgb), 0.06);
                &::before {
                    background-color: var(--clear-blue);
                }
            }
        }
    }
    .feature-title {
        font-size: 14px;
        color: var(--ori-fee-color);
        font-weight: 600;
        line-height: 16px;
        // height: 16px;
        width: 100%;
        padding: 25px 20px 0;
        border-top: 1px solid var(--line-color);
    }
    &.business {
        --background-color: #f2fff8;
        --bold-color: #20c472;
        --border-color: #20c472;
        .package-features {
            .package-feature-item {
                background-color: #20c4720f;
                &::before {
                    background-color: #20c472 !important;
                }
            }
        }
    }
    .check-icon {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        // width: 50px;
        // height: 50px;
        // text-align: center;
        // line-height: 50px;
        // border-radius: 0 12px 0 12px;
        // background-color: #ffffff;
    }
    .package-name {
        color: var(--header-color);
        margin-top: 36px;
        text-align: center;
        font-size: 22px;
        font-weight: bold;
    }
    .package-cur-fee {
        margin-top: 20px;
        font-size: 36px;
        font-weight: 900;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: var(--bold-color);
    }
    .package-origin-fee {
        margin-top: 8px;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-decoration: line-through;
        text-align: center;
        color: var(--ori-fee-color);
    }
    .package-stars {
        color: #fff;
        margin: 20px 0;
        background-image: linear-gradient(to right, #ffd200, #ffa600 100%);
        padding-left: 20px;
        padding-top: 15px;
        padding-bottom: 18px;
        .package-star-item {
            .icon-collection_02 {
                font-size: 10px;
            }
            font-size: 12px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 9px;
            &:nth-last-of-type(1) {
                margin-bottom: 0;
            }
        }
    }
    .package-features {
        padding: 20px 15px;
        flex-grow: 1;
        .package-feature-item {
            font-size: 13px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.38;
            margin-bottom: 8px;
            letter-spacing: normal;
            padding: 7px 15px;
            display: flex;
            align-items: center;
            border-radius: 18px;
            &::before {
                content: "";
                display: block;
                width: 6px;
                height: 6px;
                border-radius: 3px;
                margin: 6px 10px 6px 0;
                opacity: 0.2;
                background-color: #e1e2e6;
            }
            &:nth-last-of-type(1) {
                margin-bottom: 0;
            }
            b {
                color: var(--bold-color);
                margin-right: 5px;
            }
            .normal {
                color: var(--normal-color);
            }
        }
    }
}
</style>
