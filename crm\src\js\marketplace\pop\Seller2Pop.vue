<template>
    <PopWin
        :isShow="isShow"
        @close="closePop"
        v-loading="isLoading"
        :class="['seller2-pop', 'seller2-pop-' + theme]"
        :title="dialogTitle"
    >
        <template #content>
            <div :class="['derect-content', 'bg-' + theme]">
                <div class="content-box">
                    <div class="left">
                        <div class="derect-swiper">
                            <div class="derect-banner">
                                <img
                                    v-for="(img, index) in images"
                                    :key="index"
                                    :src="img.url"
                                    v-show="index === currentIndex"
                                />
                            </div>
                            <div class="point">
                                <div
                                    v-for="(img, index) in images"
                                    :key="index"
                                    @click="gotoPage(index)"
                                    :class="{ select: index === currentIndex }"
                                    class="little-point"
                                ></div>
                            </div>
                        </div>
                    </div>
                    <div class="right">
                        <!-- <div :class="['right-title', 'right-title-' + theme]">
                            <span class="title-one">{{
                                $t("seller2AdsPop.text0")
                            }}</span>
                            <span class="title-two">
                                {{ $t("seller2AdsPop.text1") }}</span
                            >
                        </div> -->
                        <div class="tips-card">
                            <div class="tips-content">
                                {{ $t("seller2AdsPop.text2") }}
                            </div>
                        </div>
                        <ul>
                            <li>
                                <p>{{ $t("seller2AdsPop.text3") }}</p>
                                <span>{{ $t("seller2AdsPop.text7") }}</span>
                            </li>
                            <li>
                                <p>{{ $t("seller2AdsPop.text4") }}</p>
                                <span>{{ $t("seller2AdsPop.text8") }}</span>
                            </li>
                            <li>
                                <p>{{ $t("seller2AdsPop.text5") }}</p>
                                <span>{{ $t("seller2AdsPop.text9") }}</span>
                            </li>
                            <li>
                                <p>{{ $t("seller2AdsPop.text6") }}</p>
                                <span>{{ $t("seller2AdsPop.text10") }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="btn-box">
                    <div class="derect-footer">
                        <button class="chime-btn primary" @click="confirm">
                            {{ $t("seller2AdsPop.confirmText") }}
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </PopWin>
</template>

<script>
import { components } from "common";
const { PopWin } = components;

export default {
    langModule: "marketPlace",
    components: { PopWin },
    data() {
        return {
            isShow: false,
            isLoading: false,
            images: [
                {
                    url: `https://static.chimeroi.com/servicetool-temp/2023911/17/crm/01.png`
                },
                {
                    url: `https://static.chimeroi.com/servicetool-temp/2022119/18/SellerAds2/02.png`
                },
                {
                    url: `https://static.chimeroi.com/servicetool-temp/2025523/7/c828a08a23204a05_loftyworks2`
                }
            ],
            currentIndex: 0,
            timer: null,
            theme: "common" // 'common', 'blackFriday', 'christmas', 'newYear'
        };
    },
    computed: {
        dialogTitle({ $t }) {
            return $t("seller2AdsPop.dialogTitle");
        }
    },
    methods: {
        init(opt = {}) {
            this.isShow = true;
            this.theme = opt.theme;
        },
        closePop() {
            this.$emit("ok", { isClose: true });
            this.close();
        },
        close() {
            this.isShow = false;
        },
        confirm() {
            this.$emit("ok"); //  jump to seller2.0 purchase page
            this.close();
        },
        autoplay() {
            this.currentIndex++;
            if (this.currentIndex === this.images.length) {
                this.currentIndex = 0;
            }
        },
        play() {
            setInterval(this.autoplay, 4000);
        },
        gotoPage(index) {
            this.currentIndex = index;
        }
    },
    mounted() {
        this.play();
    }
};
</script>

<style lang="less">
.seller2-pop {
    .pop-container {
        width: 940px;
    }
    .derect-content {
        display: flex;
        flex-direction: column;
        .btn-box {
            margin-bottom: 40px;
            text-align: center;
            font-size: 14px;
            .derect-footer {
                .chime-btn {
                    width: 300px;
                }
            }
        }
        &.bg-blackFriday {
            background: url("https://static.chimeroi.com/servicetool-temp/2022119/18/SellerAds2/BlackFriday.png")
                no-repeat center/cover;
        }
        &.bg-christmas {
            background: url("https://static.chimeroi.com/servicetool-temp/2022119/18/SellerAds2/Christmas.png")
                no-repeat center/cover;
        }
        &.bg-newYear {
            background: url("https://static.chimeroi.com/servicetool-temp/2022119/18/SellerAds2/NewYear.png")
                no-repeat center/cover;
        }
    }
    .content-box {
        display: flex;
        align-items: stretch;
        .left {
            margin: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            img {
                width: 450px;
            }
            .point {
                display: flex;
                justify-content: center;
                margin-top: 10px;
            }
            .little-point {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                opacity: 0.1;
                margin: 0 5px;
                background-color: #000000;
                cursor: pointer;
            }
            .select {
                opacity: 1;
                background-color: var(--primary-color);
            }
        }
        .right {
            margin: 30px 30px 30px 0;
            .right-title {
                font-weight: 600;
                line-height: 1.5;
                color: #202437;
                font-size: 24px;
                display: flex;
                flex-direction: column;
                margin-bottom: 25px;
                text-transform: uppercase;
            }
            .tips-card {
                margin-bottom: 25px;
                background: linear-gradient(90deg, #f5fbff 0%, #e7f6ff 100%);
                border-radius: 4px;
                color: #4399ed;
                border-left: 4px solid #6bcbff;
                padding: 10px 13px 10px 10px;
                box-sizing: border-box;
                font-style: italic;
                font-weight: 600;
                font-size: 15px;
                line-height: 20px;
                .tips-content {
                    &::before {
                        content: "*";
                        color: #4399ed;
                    }
                }
            }
            ul {
                margin-left: 20px;
                li {
                    margin-bottom: 15px;
                    list-style-type: disc;
                    color: #ebecf1;
                    &:nth-last-child(1) {
                        margin-bottom: 0;
                    }
                    &::marker {
                        font-size: 20px;
                        color: rgba(0, 0, 0, 0.1);
                        line-height: 24px;
                    }
                    p {
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 21px;
                        color: #515666;
                        margin-bottom: 5px;
                    }
                    span {
                        font-size: 12px;
                        line-height: 16px;
                        color: #797e8b;
                        display: flex;
                    }
                }
            }
        }
    }
    .pop-footer {
        display: none;
    }
    &.seller2-pop-blackFriday {
        .pop-container {
            background: #000;
        }
        .pop-title {
            color: #fff;
            border-bottom: 1px solid #202437;
            .icon-cancel_bold {
                &:hover {
                    color: #fff;
                }
            }
        }
        .derect-content {
            color: #fff;
        }
        .content-box {
            .left {
                .little-point {
                    background-color: #fff;
                    opacity: 0.2;
                }
                .select {
                    opacity: 1;
                    background-color: #f0454c;
                }
            }
            .right {
                margin: 55px 30px 50px 0;
                .right-title-blackFriday {
                    font-weight: 800;
                    font-size: 24px;
                    line-height: 1.5;
                    .title-one {
                        color: #fff;
                    }
                    .title-two {
                        color: #f0454c;
                    }
                }
                ul {
                    li {
                        p {
                            color: #fff;
                            opacity: 0.8;
                        }
                        span {
                            color: #fff;
                            opacity: 0.6;
                        }
                        &::marker {
                            color: rgba(255, 255, 255, 0.2);
                        }
                    }
                }
                .tips-card {
                    background: rgba(255, 255, 255, 0.1);
                    border-left: 4px solid #f0454c;
                    color: #fff;
                    .tips-content {
                        &::before {
                            content: "*";
                            color: #fff;
                        }
                    }
                }
            }
        }
        .btn-box {
            margin-bottom: 54px;
            .derect-footer {
                .chime-btn {
                    background: #f0454c;
                    &:hover {
                        background: #c2313c;
                    }
                }
            }
        }
    }
    &.seller2-pop-christmas {
        .pop-container {
            background: #eee7dc;
        }
        .pop-title {
            color: #202437;
            border-bottom: 1px solid rgba(32, 36, 55, 0.1);
            .icon-cancel_bold {
                &:hover {
                    color: #fff;
                }
            }
        }
        .derect-content {
            color: #fff;
        }
        .content-box {
            .left {
                .little-point {
                    background-color: #000;
                    opacity: 0.1;
                }
                .select {
                    opacity: 1;
                    background-color: #f0454c;
                }
            }
            .right {
                margin: 55px 30px 50px 0;
                .right-title-christmas {
                    font-weight: 600;
                    font-size: 24px;
                    line-height: 1.5;
                    .title-one {
                        color: #202437;
                    }
                    .title-two {
                        color: #202437;
                    }
                }
                ul {
                    li {
                        p {
                            color: #515666;
                        }
                        span {
                            color: #797e8b;
                        }
                        &::marker {
                            color: rgba(0, 0, 0, 0.1);
                        }
                    }
                }
                .tips-card {
                    background: linear-gradient(
                        90deg,
                        rgba(240, 69, 76, 0.05) 0%,
                        rgba(240, 69, 76, 0.15) 100%
                    );
                    border-left: 4px solid #f0454c;
                    color: #515666;
                    .tips-content {
                        &::before {
                            content: "*";
                            color: #515666;
                        }
                    }
                }
            }
        }
        .btn-box {
            margin-bottom: 54px;
            .derect-footer {
                .chime-btn {
                    background: #f0454c;
                    &:hover {
                        background: #c2313c;
                    }
                }
            }
        }
    }
    &.seller2-pop-newYear {
        .pop-container {
            background: #11152a;
        }
        .pop-title {
            color: #fff;
            border-bottom: 1px solid #202437;
            .icon-cancel_bold {
                &:hover {
                    color: #fff;
                }
            }
        }
        .derect-content {
            color: #fff;
        }
        .content-box {
            .left {
                .little-point {
                    background-color: #fff;
                    opacity: 0.2;
                }
                .select {
                    opacity: 1;
                    background-color: #f0b37c;
                }
            }
            .right {
                margin: 55px 30px 50px 0;
                .right-title-newYear {
                    font-weight: 800;
                    font-size: 24px;
                    line-height: 1.5;
                    .title-one {
                        color: #ffffff;
                    }
                    .title-two {
                        color: #ffd098;
                    }
                }
                ul {
                    li {
                        p {
                            color: #fff;
                            opacity: 0.8;
                        }
                        span {
                            color: #fff;
                            opacity: 0.6;
                        }
                        &::marker {
                            color: rgba(255, 255, 255, 0.2);
                        }
                    }
                }
                .tips-card {
                    background: rgba(255, 255, 255, 0.1);
                    border-left: 4px solid #ffd098;
                    color: #fff;
                    .tips-content {
                        &::before {
                            content: "*";
                            color: #fff;
                        }
                    }
                }
            }
        }
        .btn-box {
            margin-bottom: 54px;
            color: #202437;
            .derect-footer {
                .chime-btn {
                    background: #ffd098;
                    color: #202437;
                    &:hover {
                        background: #ebc18f;
                    }
                }
            }
        }
    }
}
</style>
