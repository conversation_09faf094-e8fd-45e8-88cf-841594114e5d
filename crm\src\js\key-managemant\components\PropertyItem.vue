<template>
    <div class="property-item">
        <div class="property-image">
            <img :src="imageUrl" :alt="property.address" />
        </div>
        <div class="property-info">
            <div class="property-address" :title="property.address">
                {{ property.address }}
            </div>
            <div class="property-id">
                ID: {{ property.keyId }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PropertyItem',
    props: {
        property: {
            type: Object,
            required: true
        }
    },
    computed: {
        imageUrl() {
            // 使用随机图片服务，基于keyId生成固定的随机图片
            const seed = this.property.keyId || Math.random();
            return `https://picsum.photos/seed/${seed}/64/48`;
        }
    }
};
</script>

<style scoped>
.property-item {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.property-image {
    flex-shrink: 0;
}

.property-image img {
    width: 64px;
    height: 48px;
    border-radius: 6px;
    object-fit: cover;
}

.property-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.property-address {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.property-id {
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 12px;
    line-height: 1.33;
    color: #797E8B;
}
</style>
