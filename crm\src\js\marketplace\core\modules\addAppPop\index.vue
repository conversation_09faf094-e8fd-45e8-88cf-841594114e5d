<template>
    <div :class="{ 'app-operate-mask': isShowPop }">
        <div class="app-operate-pop" :class="{ isShowPop: isShowPop }">
            <div class="app-operate-pop-header">
                <span class="app-operate-pop-header_text">{{ $t("addPop.title1") }}</span>
                <div class="app-operate-pop-header_cancel" @click="closePop(false)">
                    <i class="icon2017 icon-cancel_bold"></i>
                </div>
            </div>
            <div class="app-pop-body" v-loading="isLoading">
                <div class="search">
                    <Input
                        size="medium"
                        :placeholder="$t('addPop.placeholder1')"
                        :value="searchValue"
                        @input="inputSearchValue"
                    >
                        <template #prefix>
                            <i class="icon2017 icon-search_01 search_icon"></i>
                        </template>
                    </Input>
                </div>
                <div class="card-result" @scroll="scroll">
                    <div class="card-module" v-for="(item, index) in list" :key="item.id">
                        <div ref="cardModuleTitle" class="card-module_title">
                            <div
                                class="card-module_title-inner"
                                :style="index | floatStyle(floatIndex)"
                            >
                                <span>{{ item.title }}</span>
                                <div
                                    class="app-add-box"
                                    v-if="item.type === 'VENDOR_APP'"
                                    @click.stop="operateVendorApp"
                                >
                                    <span class="icon2017 icon-add_bold"></span>
                                    <span>{{ $t("btns.addNewApp") }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-module__body">
                            <MarketCard
                                class="card-option"
                                :class="{ 'btn-disabled': card.btnDisabled }"
                                v-for="(card, pos) in item.appList"
                                :key="card.id"
                                v-bind="card"
                                currentMode="editing"
                                :environment="environment"
                                :buttonText="card.btnText"
                                v-tip="bundleCardTip(card)"
                                @triggerClick="clickMPApp({ data: card })"
                            >
                                <template #editIconBox v-if="card.type === 'CUSTOM_APP'">
                                    <div class="icon-box hander-left">
                                        <span
                                            class="icon2017 icon-draw_05"
                                            @click.stop="operateVendorApp(card, index, pos)"
                                        ></span>
                                    </div>
                                </template>
                                <template #selectIconBox>
                                    <div class="icon-box right hander-right">
                                        <CheckBox
                                            :checked="selectApp.id === card.id"
                                            :disabled="card.btnDisabled"
                                            @change="selectMpApp($event.checked, card)"
                                        ></CheckBox>
                                    </div>
                                </template>
                            </MarketCard>
                            <div class="vendor-empty" v-if="isShowVendorEmpty(item)">
                                <i class="icon2017 icon-drawar_03"></i>
                                <p
                                    class="vendor-empty_tip"
                                    v-if="customizeThirdPartyAddon"
                                    v-html="$t('addPop.vendorEmptyTip1')"
                                    v-click="{
                                        selector: '.vendor-click',
                                        callback: operateVendorApp
                                    }"
                                ></p>
                                <p
                                    v-else
                                    class="vendor-empty_tip"
                                    v-html="$t('addPop.vendorEmptyTip3')"
                                ></p>
                            </div>
                        </div>
                    </div>
                    <div class="empty" v-show="isEmpty">
                        <i class="icon2017 icon-doc"></i>
                        <p
                            class="empty_tip"
                            v-if="customizeThirdPartyAddon"
                            v-html="$t('addPop.vendorEmptyTip2')"
                            v-click="{
                                selector: '.vendor-click',
                                callback: operateVendorApp
                            }"
                        ></p>
                        <p
                            v-else
                            class="vendor-empty_tip"
                            v-html="$t('addPop.vendorEmptyTip4')"
                        ></p>
                    </div>
                </div>
            </div>
            <div class="app-pop-footer">
                <button class="chime-btn invisible" @click="closePop(false)">Cancel</button>
                <button class="chime-btn primary" :disabled="disabled" @click="confirm()">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import { components, utils } from "common";
import MarketCard from "../../components/card";
import { getCanAddMPList } from "../../utils/api";
import { getMarketPlaceConfig } from "../../store/calculate";
import { createVendorAppPop } from "../index";
import store from "../../store/index";
import { mapState, mapMutations } from "vuex";
import { basePermission } from "crm";
import lodash from "lodash";
import { brandBundleList } from "../../config";

const { Input, CheckBox } = components;
const dialerList = ["dialer", "text", "threeLineDialer", "unlimitedText"];
const adsList = [
    "facebookAds",
    "googleAds",
    "listingAds",
    "fbSellerAds",
    "fbSellerAds2",
    "bingAds",
    "remarketingAds",
    "googleLocalServicesAds"
];

export default {
    langModule: "marketPlace",
    name: "AddAppPop",
    store,
    data() {
        return {
            isShowPop: false,
            isLoading: false,
            searchValue: "",
            floatIndex: -1,
            selectApp: {},
            list: [],
            allList: [],
            filterApps: [],
            addApps: [],
            dialerList: Object.freeze(dialerList),
            adsList: Object.freeze(adsList),
            currCateIds: [],
            categoryList: [],
            fromCategoryId: ""
        };
    },
    components: {
        Input,
        MarketCard,
        CheckBox
    },
    computed: {
        ...mapState(["id", "apiEnv", "environment", "cardCategoryIdMap", "normalRenderAppList"]),
        disabled({ selectApp }) {
            // eslint-disable-next-line no-prototype-builtins
            return !selectApp.hasOwnProperty("id");
        },
        isEmpty({ isLoading, list }) {
            const isNotData =
                list.filter((v) => {
                    return v?.appList?.length > 0;
                }).length <= 0;
            return !isLoading && isNotData;
        },
        customizeChimeAddon() {
            return basePermission.customizeChimeAddon;
        },
        customizeThirdPartyAddon() {
            return basePermission.customizeThirdPartyAddon;
        }
    },
    filters: {
        floatStyle(index, floatIndex) {
            return floatIndex === index
                ? {
                      position: "absolute",
                      top: "76px",
                      left: "0"
                  }
                : {};
        }
    },
    methods: {
        ...mapMutations(["updateCardCategoryIdMap"]),
        bundleCardTip(data) {
            if (!data.btnDisabled) {
                return {};
            }
            const liList = [
                "Social Studio",
                "Power Dialer",
                "LoftyWorks Present Personal",
                "Agent Website"
            ].map((i) => {
                return `<li style="display: flex; align-items: center">
                    <span style="display: block; margin-right: 5px; width: 8px; height: 8px; border-radius: 50%; background: #C6C8D1; margin-right: 5px"></span>${i}
                </li>`;
            });
            return {
                content: `<div style="line-height: 20px; font-size: 12px; color: #C6C8D1">
                    <div>To use the Branding Bundle, make sure all of the following products are available for purchase in Marketplace:</div>
                    <ul style="margin-top: 10px;">
                        ${liList.join("")}
                    </ul>
                </div>`,
                maxWidth: "400px",
                placement: "bottom"
            };
        },
        isBtnDisabled(data) {
            if (data.appKey !== "brandBundle") {
                return false;
            }
            const flag = brandBundleList.every((v) =>
                this.normalRenderAppList.find((v2) => v2.appKey === v && v2.hasShowAuth)
            );
            return !flag;
        },
        initialization({
            filterApps = [],
            addApps = [],
            currCateIds = [],
            categoryList = [],
            fromCategoryId = ""
        } = {}) {
            this.isShowPop = true;
            this.filterApps = Object.freeze(filterApps);
            this.addApps = addApps;
            this.currCateIds = currCateIds;
            this.categoryList = categoryList;
            this.fromCategoryId = fromCategoryId;
            this.search();
        },
        clickMPApp({ data }) {
            if (data.btnDisabled) {
                return;
            }
            if (this.selectApp?.id === data?.id) {
                this.selectApp = {};
            } else {
                this.selectApp = { ...data };
            }
        },
        selectMpApp(checked, data) {
            this.selectApp = checked ? data : {};
        },
        operateVendorApp(card = {}, oneIndex = -1, twoIndex = -1) {
            const { categoryList, currCateIds } = this;
            let categoryIds = [];
            let currSelectApp = {};

            if (card.id) {
                currSelectApp = Object.assign({}, card);
                categoryIds = card.categoryIds?.length
                    ? card.categoryIds
                    : this.cardCategoryIdMap.get(card.id) || [];
            }
            createVendorAppPop({
                config: card,
                categoryList,
                currCateIds: card.id ? categoryIds : currCateIds
            }).then(async ({ type = false, data = {}, operate }) => {
                if (type) {
                    //  when new ， Need to re-request the interface to get data
                    if (operate === "add") {
                        this.searchValue = "";
                        await this.search();
                        this.selectApp = { ...data, hasShowAuth: true };
                        currSelectApp = Object.assign({}, this.selectApp);
                    } else {
                        const editCard = {
                            ...card,
                            ...data
                        };
                        this.list[oneIndex].appList.splice(twoIndex, 1, editCard);
                        //  when editorial app for selected app hour ， Need to modify selected app data
                        editCard.id === this.selectApp.id && (this.selectApp = editCard);

                        currSelectApp = Object.assign({}, editCard);
                    }

                    // update
                    // const cloneCardCategoryIdMap = lodash.cloneDeep(this.cardCategoryIdMap)
                    // cloneCardCategoryIdMap.set(this.selectApp.id, this.selectApp.categoryIds)
                    // this.updateCardCategoryIdMap(cloneCardCategoryIdMap)

                    if (currSelectApp.categoryIds?.length > 0) {
                        this.selectApp = currSelectApp;
                        this.confirm(true);
                    }
                }
            });
        },
        inputSearchValue(value = "") {
            this.searchValue = value;
            this.search();
        },
        isShowVendorEmpty({ type, appList = [] } = {}) {
            return type === "VENDOR_APP" && appList.length <= 0;
        },
        search: utils.debounce(async function () {
            const { filterApps, addApps, searchValue } = this;
            let temporarilyHiddenApps = JSON.parse(JSON.stringify(addApps));
            this.isLoading = true;
            let list = await getCanAddMPList(
                {
                    settingId: this.id,
                    content: searchValue
                },
                this.apiEnv
            );
            //TODO temp delete homeowner
            list = list.map((subItem) => {
                return {
                    ...subItem,
                    appList: subItem.appList.filter((d) => d.appKey !== "homeowner")
                };
            });

            //  temporarily hidden on the interface app， Need to compare interface data ， Find out the temporary hiding of the non-repeating interface app data set
            if (temporarilyHiddenApps.length) {
                //  remove duplicates
                list.forEach((oneLevel) => {
                    let { appList = [] } = oneLevel;

                    //  already exists on the interface app Need to filter
                    appList = appList.filter((v) => {
                        //  judge app Whether it exists in the temporarily hidden app in the collection
                        temporarilyHiddenApps = temporarilyHiddenApps.filter((temporarily) => {
                            // backend search will return the third app whether it is added to page, so the frontend will not handler vendor app
                            if (v.type !== "CHIME_APP") {
                                return true;
                            }
                            return temporarily.id !== v.id;
                        });
                        return (
                            (v.type !== "CHIME_APP" || !filterApps.includes(v.id)) &&
                            this.havePermission(v)
                        );
                    });

                    oneLevel.appList = appList;
                });
            }

            //  do what has been chosen app filter ， At the same time, temporarily hide the app Data added into
            if (filterApps.length || temporarilyHiddenApps.length) {
                //  Convert to lowercase filter
                const filterContent = searchValue.trim().toLocaleLowerCase();

                list.forEach((oneLevel) => {
                    let { appList = [], type } = oneLevel;
                    let temporarily = [];

                    //  already exists on the interface app Need to filter
                    appList = appList.filter((v) => {
                        return (
                            (v.type !== "CHIME_APP" || !filterApps.includes(v.id)) &&
                            this.havePermission(v)
                        );
                    });

                    // backend search will return the third app whether it is added to page, so the frontend will not handler vendor app
                    if (type === "VENDOR_APP") {
                        temporarily = [];
                    } else {
                        //  Filter by category to temporarily hide the interface app data
                        temporarily = temporarilyHiddenApps.filter((v) => {
                            let { title = "", descripe = "" } = v;
                            //  convert to lower case
                            title = title.toLocaleLowerCase();
                            descripe = descripe.toLocaleLowerCase();
                            //  filter by filter
                            let matchFilter = filterContent
                                ? title.includes(filterContent) || descripe.includes(filterContent)
                                : true;

                            return v.type === "CHIME_APP" && matchFilter;
                        });
                    }

                    appList.forEach((app) => {
                        if (this.cardCategoryIdMap.has(app.id)) {
                            app.categoryIds = this.cardCategoryIdMap.get(app.id) || [];
                        }
                    });

                    //  Join the interface to temporarily hide app
                    appList.push(...temporarily);
                    //  rearrange
                    appList.sort((a, b) => {
                        //  judgment rule
                        if (type === "VENDOR_APP") {
                            //  front to back in time
                            return a.createTime - b.createTime;
                        } else {
                            //  by letter a-z
                            let aTitle = a?.title || "";
                            let bTitle = b?.title || "";
                            return aTitle.localeCompare(bTitle);
                        }
                    });

                    oneLevel.appList = appList;
                });
            }

            let hasNotvendorApp =
                list.filter((v) => v?.appList?.length > 0 && v.type !== "VENDOR_APP").length > 0;

            list = list.filter(
                (v) => v?.appList?.length > 0 || (hasNotvendorApp && v.type === "VENDOR_APP")
            );

            if (list.length) {
                const { renderList } = await getMarketPlaceConfig({
                    mode: "editing",
                    interfaceList: list,
                    handleLevel: 2
                });

                this.list = renderList.map((oneLevel) => {
                    const appList = oneLevel.appList.map((v) => {
                        if (v.appKey === "brandBundle") {
                            v.btnDisabled = this.isBtnDisabled(v);
                        }
                        return v;
                    });
                    oneLevel.appList = appList;
                    return oneLevel;
                });
            } else {
                this.list = [];
            }
            //  If there is no tripartite authority ， hide Your Apps/Services module
            if (!this.customizeThirdPartyAddon) {
                this.list = this.list.filter((i) => i.type != "VENDOR_APP");
            }

            if (!searchValue) {
                this.allList = lodash.cloneDeep(this.list);
            }

            this.isLoading = false;
        }, 60),
        havePermission(app) {
            /**
             *  Filter by permissions app:
             * 1. only chime addon permissions ， filter thirty party app
             * 2. only thirty party addon permissions ， filter chime addon app
             */
            /**
             * type: CUSTOM_APP、CHIME_APP、INTEGRATION_SYSTEM_APP、INTEGRATION_ADMIN_APP
             */
            let havePermission = true;
            if (this.customizeChimeAddon && !this.customizeThirdPartyAddon) {
                havePermission =
                    app.type != "INTEGRATION_ADMIN_APP" &&
                    app.type != "INTEGRATION_SYSTEM_APP" &&
                    app.type != "CUSTOM_APP";
            } else if (this.customizeThirdPartyAddon && !this.customizeChimeAddon) {
                havePermission = app.type != "CHIME_APP";
            } else if (this.customizeChimeAddon && this.customizeThirdPartyAddon) {
                havePermission = true;
            }
            return havePermission;
        },
        scroll(e) {
            const rootContent = e.target;
            const scrollContentTop = rootContent.getBoundingClientRect().top;
            const targetGather = this.$refs.cardModuleTitle;
            let floatIndex = -1;

            for (let i = 0; i < targetGather.length; i++) {
                let ele = targetGather[i];
                let targetTop = ele.getBoundingClientRect().top;
                let offsetTop = targetTop - scrollContentTop;

                if (offsetTop < 0) {
                    floatIndex = i;
                } else {
                    break;
                }
            }

            this.floatIndex = floatIndex;
        },
        closePop(type = false, data = []) {
            this.isShowPop = false;
            this.$emit("close", { type, data });
        },
        confirm(addEditFlag) {
            const { selectApp, disabled, dialerList, adsList, allList, fromCategoryId } = this;

            if (!addEditFlag) {
                let { categoryIds } = this.selectApp;

                categoryIds.push(fromCategoryId);

                categoryIds = [...new Set(categoryIds)];

                this.selectApp.categoryIds = categoryIds;
            }

            if (!disabled) {
                const apps = [];
                let linkageApps = [];

                if (dialerList.includes(selectApp.appKey)) {
                    linkageApps = dialerList;
                } else if (adsList.includes(selectApp.appKey)) {
                    linkageApps = adsList;
                }
                //  Linkage to add logic
                if (linkageApps.length) {
                    allList.forEach((oneLevel) => {
                        const { appList = [] } = oneLevel;
                        const temporary = appList.filter((v) => linkageApps.includes(v.appKey));

                        apps.push(...temporary);
                    });
                } else {
                    apps.push(selectApp);
                }
                this.closePop(true, apps);
            }
        }
    }
};
</script>
<style lang="less" scoped>
.app-operate-mask {
    width: 100%;
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    background: #20243782;
    z-index: 100;
}
.app-operate-pop {
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: -10px 0px 20px rgba(32, 36, 55, 0.05);
    position: fixed;
    top: 0;
    right: -800px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    transition: right 0.3s;
    width: 800px;
    &.isShowPop {
        right: 0;
    }
    .app-operate-pop-header {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        height: 50px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px 0 20px;

        &_text {
            font-weight: 700;
            font-size: 16px;
            line-height: 20px;
            color: var(--label-color);
        }
        &_cancel {
            width: 26px;
            height: 26px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:hover {
                background-color: rgba(var(--primary-color-rgb), 0.1);

                i {
                    color: var(--primary-color);
                }
            }
            &:active {
                background-color: rgba(var(--primary-color-rgb), 0.3);
            }
            i {
                color: #a0a3af;
                font-size: 14px;
            }
        }
    }
    .app-pop-body {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        position: relative;
    }
    .search {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #fff;
        padding: 20px 20px 0;
        margin-bottom: 10px;

        &_icon {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin: 0 10px;
            line-height: 17px;
        }
    }
    .card-result {
        flex: 1;
        width: 100%;
        margin-top: 76px;
        // max-height: 500px;
        overflow: auto;
        box-sizing: border-box;
        scrollbar-width: none; /* firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
            display: none; /* Chrome Safari */
        }
    }
    .card-module {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        margin-bottom: 30px;

        &_title {
            width: 100%;
            height: 50px;
            overflow: hidden;
            box-sizing: border-box;

            &-inner {
                width: 100%;
                height: 50px;
                padding: 8px 20px;
                font-weight: 700;
                font-size: 16px;
                line-height: 24px;
                color: var(--label-color);
                margin-bottom: 10px;
                background-color: #fff;
                box-sizing: border-box;
                z-index: 10;
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 40px;

                .app-add-box {
                    cursor: pointer;
                    color: var(--primary-color);
                    background: rgba(var(--primary-color-rgb), 0.1);
                    border-radius: 15px;
                    font-weight: 400;
                    line-height: 20px;
                    padding: 2px 10px;
                    font-size: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .icon2017 {
                        margin-right: 5px;
                    }
                    span {
                        font-size: 12px;
                    }
                    &:hover {
                        background: rgba(var(--primary-color-rgb), 0.2);
                    }
                    &:active {
                        background: rgba(var(--primary-color-rgb), 0.3);
                    }
                }
            }
        }
        &__body {
            width: 100%;
            padding: 0 20px;
            box-sizing: border-box;
            display: flex;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;

            :deep(.market-card) {
                cursor: pointer;
                width: calc(50% - 5px);
                margin-bottom: 10px;
                .hander-left {
                    left: 10px;
                    .icon-draw_05 {
                        &:hover {
                            background: var(--primary-color);
                            color: #fff;
                            cursor: pointer;
                        }
                        &:active {
                            background: linear-gradient(
                                    0deg,
                                    rgba(0, 0, 0, 0.1),
                                    rgba(0, 0, 0, 0.1)
                                ),
                                var(--primary-color);
                        }
                    }
                }
            }
        }
        .card-option {
            &.btn-disabled {
                cursor: not-allowed;
                .hander-right {
                    cursor: not-allowed;
                }
                :deep(.com-checkbox) {
                    pointer-events: none;
                    .checkbox-check {
                        cursor: not-allowed;
                    }
                }
            }
            .hander-left {
                display: none;
            }
            .hander-right {
                font-size: 12px;
                :deep(.com-checkbox) {
                    .modify-check-box {
                        &::before {
                            top: 0;
                            left: 0;
                            color: var(--primary-color);
                        }
                    }
                    .checkbox-check {
                        width: 18px;
                        height: 18px;
                        border-radius: 100%;
                        margin: 0;
                        background: #fafafd;
                        border: 1px solid #c6c8d1;
                        border-radius: 4px;

                        &.checked {
                            .modify-check-box();
                        }
                    }
                }
            }
            &:hover {
                .hander-left {
                    display: block;
                }
            }
        }
    }
    .empty {
        width: 100%;
        height: 400px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        .icon-doc {
            font-size: 40px;
            color: #c6c8d1;
        }
        &_tip {
            margin-top: 20px;
            font-weight: 400;
            font-size: 12px;
            line-height: 14px;
            text-align: center;
            color: #c6c8d1;

            :deep(span, .vendor-click) {
                color: var(--primary-color);
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    .vendor-empty {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        padding: 40px 0;

        i {
            font-size: 40px;
            color: #c6c8d1;
        }
        &_tip {
            color: #c6c8d1;
            font-size: 12px;
            line-height: 20px;
            margin-top: 3px;

            :deep(span, .vendor-click) {
                color: var(--primary-color);
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    .app-pop-footer {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        height: 66px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 20px;
        button {
            width: 110px;
            height: 36px;
            & + button {
                margin-left: 10px;
            }
        }
    }
}
</style>
