<template>
    <div class="status-badge">
        <span class="status-dot" :style="{ backgroundColor: statusColor }"></span>
        <span class="status-text">{{ statusText }}</span>
    </div>
</template>

<script>
export default {
    name: 'StatusBadge',
    props: {
        status: {
            type: String,
            required: true
        }
    },
    computed: {
        statusConfig() {
            const configs = {
                'available': {
                    text: 'Available',
                    color: '#20C472'
                },
                'checked_out': {
                    text: 'Checked Out',
                    color: '#5D51E2'
                },
                'archived': {
                    text: 'Archived',
                    color: '#C6C8D1'
                },
                'lost_damaged': {
                    text: 'Lost/Damaged',
                    color: '#F0454C'
                }
            };
            return configs[this.status] || configs['available'];
        },
        statusText() {
            return this.statusConfig.text;
        },
        statusColor() {
            return this.statusConfig.color;
        }
    }
};
</script>

<style scoped>
.status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    opacity: 0.8;
}

.status-text {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}
</style>
