<template>
  <aside class="side">
    <div class="info">
      <div class="img">
        <img src="https://via.placeholder.com/150" alt="property image" />
      </div>
      <div class="mixed">
        <div class="tag-area">
          <div class="tag">For Sale</div>
          <div class="tag instructed">Instructed <img src="https://via.placeholder.com/150" alt="caret down" /></div>
        </div>
        <div class="title-area">
          <div class="title">
            <h1>Flat 5, 123 Baker Street</h1>
            <div class="icon-area">
              <img src="https://via.placeholder.com/150" alt="location icon" />
            </div>
          </div>
          <p>Marylebone, London NW1 6XE</p>
          <div class="text">
            <div class="text-item">
              <img src="https://via.placeholder.com/150" alt="bedroom icon" />
              <span>7BD</span>
            </div>
            <div class="vline"></div>
            <div class="text-item">
              <img src="https://via.placeholder.com/150" alt="bathroom icon" />
              <span>10BA</span>
            </div>
            <div class="vline"></div>
            <div class="text-item">
              <img src="https://via.placeholder.com/150" alt="reception icon" />
              <span>10 Receptions</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="con">
      <div class="con-item">
        <div class="title">
          <h2>Key Info</h2>
          <div class="right">
            <div class="icon-area"><img src="https://via.placeholder.com/150" alt="edit icon" /></div>
            <div class="arrow"><img src="https://via.placeholder.com/150" alt="arrow down" /></div>
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <p>Property Status:</p>
            <p>Available</p>
          </div>
          <div class="info-item">
            <p>Property Type:</p>
            <p>Residential</p>
          </div>
          <div class="info-item">
            <p>Price:</p>
            <p>£1,500,00</p>
          </div>
          <div class="info-item">
            <p>Tenure:</p>
            <p>Leasehold</p>
          </div>
          <div class="info-item">
            <p>Tenure Expiry Date:</p>
            <p>2055-03-01</p>
          </div>
          <div class="info-item">
            <p>Service Charge:</p>
            <p>300/year</p>
          </div>
          <div class="info-item">
            <p>Ground Rent:</p>
            <p>300</p>
          </div>
        </div>
      </div>
      <div class="con-item">
        <div class="title">
          <h2>Vendor</h2>
          <div class="right">
            <div class="icon-area"><img src="https://via.placeholder.com/150" alt="edit icon" /></div>
            <div class="arrow"><img src="https://via.placeholder.com/150" alt="arrow down" /></div>
          </div>
        </div>
        <div class="lists">
          <div class="list-item">
            <p>Jacob Jones (Primary)</p>
            <div class="rows">
              <div class="row-item">
                <div class="icon-area"><img src="https://via.placeholder.com/150" alt="call icon" /></div>
                <span>555-164-8899</span>
              </div>
              <div class="row-item">
                <div class="icon-area"><img src="https://via.placeholder.com/150" alt="mail icon" /></div>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
          <div class="list-item">
            <p>Savannah Nguyen</p>
            <div class="rows">
              <div class="row-item">
                <div class="icon-area"><img src="https://via.placeholder.com/150" alt="call icon" /></div>
                <span>555-123-9542</span>
              </div>
              <div class="row-item">
                <div class="icon-area"><img src="https://via.placeholder.com/150" alt="mail icon" /></div>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="con-item">
        <div class="title">
          <h2>Negotiators</h2>
          <div class="right">
            <div class="icon-area"><img src="https://via.placeholder.com/150" alt="edit icon" /></div>
            <div class="arrow"><img src="https://via.placeholder.com/150" alt="arrow down" /></div>
          </div>
        </div>
        <div class="lists">
          <div class="list-item-avatar">
            <div class="avatar"></div>
            <div class="rows">
              <p>Johnson Andrew (Primary)</p>
              <div class="row-item">
                <img src="https://via.placeholder.com/150" alt="call icon" />
                <span>555-164-8899</span>
              </div>
              <div class="row-item">
                <img src="https://via.placeholder.com/150" alt="mail icon" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
          <div class="list-item-avatar">
            <div class="avatar"></div>
            <div class="rows">
              <p>Michael Smith</p>
              <div class="row-item">
                <img src="https://via.placeholder.com/150" alt="call icon" />
                <span>555-123-9542</span>
              </div>
              <div class="row-item">
                <img src="https://via.placeholder.com/150" alt="mail icon" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script>
export default {
  name: 'Side',
};
</script>

<style scoped>
.side {
  width: 360px;
  border: 1px solid #ebecf1;
  border-radius: 6px;
}
.info .img img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px 6px 0 0;
}
.mixed {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  margin-top: -40px;
}
.tag-area {
  display: flex;
  gap: 5px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 40px;
  box-shadow: 0px -5px 5px 0px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}
.tag {
  padding: 0 12px;
  height: 30px;
  display: flex;
  align-items: center;
  border-radius: 30px;
  font-size: 12px;
  font-weight: 510;
}
.tag:not(.instructed) {
  background-color: rgba(121, 126, 139, 0.1);
  color: #797e8b;
}
.instructed {
  background-color: rgba(32, 196, 114, 0.1);
  color: #20c472;
}
.title-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  border-bottom: 1px solid #ebecf1;
  padding-bottom: 20px;
}
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title h1 {
  font-size: 24px;
  font-weight: 510;
  color: #202437;
}
.icon-area {
  background-color: rgba(93, 81, 226, 0.1);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  display: flex;
  gap: 10px;
  align-items: center;
}
.text-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
  color: #797e8b;
}
.vline {
  width: 1px;
  height: 12px;
  background-color: #e1e2e6;
}
.con {
  padding: 0 20px 20px;
}
.con-item {
  border-bottom: 1px solid #ebecf1;
  padding: 15px 0;
}
.con-item:last-child {
  border-bottom: none;
}
.con-item .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.con-item .title h2 {
  font-size: 16px;
  font-weight: 510;
  color: #202437;
}
.con-item .title .right {
  display: flex;
  gap: 10px;
  align-items: center;
}
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}
.info-item p:first-child {
  font-size: 14px;
  color: #797e8b;
}
.info-item p:last-child {
  font-size: 14px;
  color: #515666;
}
.lists {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.list-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.list-item > p {
  font-size: 14px;
  font-weight: 510;
  color: #515666;
}
.rows {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.row-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
  color: #797e8b;
}
.row-item .icon-area {
  background-color: rgba(93, 81, 226, 0.1);
  width: 20px;
  height: 20px;
  border-radius: 6px;
}
.list-item-avatar {
  display: flex;
  gap: 10px;
  align-items: center;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #c4c4c4;
}
.list-item-avatar .rows {
  gap: 2px;
}
.list-item-avatar .rows > p {
  font-size: 14px;
  font-weight: 510;
  color: #515666;
}
</style>