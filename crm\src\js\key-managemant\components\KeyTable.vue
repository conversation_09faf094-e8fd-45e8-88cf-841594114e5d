<template>
    <div class="key-table-wrapper">
        <DataTable
            :data="tableData"
            class="key-management-table"
            :noBorder="false"
        >
            <DataTableColumn
                label="Property Address"
                :widthRatio="3"
                :minWidth="440"
            >
                <template slot-scope="{ data }">
                    <PropertyItem :property="data" />
                </template>
            </DataTableColumn>
            
            <DataTableColumn
                label="Key Code"
                field="keyCode"
                :width="160"
            >
                <template slot-scope="{ data }">
                    <span class="key-code">{{ data.keyCode }}</span>
                </template>
            </DataTableColumn>
            
            <DataTableColumn
                label="Holder"
                field="holder"
                :width="200"
            >
                <template slot-scope="{ data }">
                    <span class="holder-name">{{ data.holder }}</span>
                </template>
            </DataTableColumn>
            
            <DataTableColumn
                label="Status"
                :width="200"
            >
                <template slot-scope="{ data }">
                    <StatusBadge :status="data.status" />
                </template>
            </DataTableColumn>
            
            <DataTableColumn
                label="Last Access"
                field="lastAccess"
                :width="200"
            >
                <template slot-scope="{ data }">
                    <span class="last-access">{{ data.lastAccess }}</span>
                </template>
            </DataTableColumn>
        </DataTable>
    </div>
</template>

<script>
import { components } from 'common';
import PropertyItem from './PropertyItem.vue';
import StatusBadge from './StatusBadge.vue';

const { DataTable } = components;

export default {
    name: 'KeyTable',
    components: {
        DataTable,
        PropertyItem,
        StatusBadge
    },
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        tableData() {
            return this.data;
        }
    }
};
</script>

<style scoped>
.key-table-wrapper {
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-top: none;
    border-radius: 0px 0px 6px 6px;
}

.key-management-table {
    width: 100%;
}

.key-code,
.holder-name,
.last-access {
    font-family: 'SF Pro Text';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

/* 表头样式 */
.key-management-table :deep(.com-datatable-header-wrap) {
    background: #FFFFFF;
}

.key-management-table :deep(.com-datatable-header-wrap .table-head-cell) {
    font-family: 'SF Pro';
    font-weight: 700;
    font-size: 14px;
    line-height: 1.43;
    color: #202437;
    padding: 12px 0;
}

/* 表格行样式 */
.key-management-table :deep(.table-body-row) {
    border-bottom: 1px solid #EBECF1;
}

.key-management-table :deep(.table-body-row:last-child) {
    border-bottom: none;
}

.key-management-table :deep(.table-body-cell) {
    padding: 20px 0;
    vertical-align: middle;
}

/* 第一列特殊样式 */
.key-management-table :deep(.table-body-cell:first-child) {
    padding-left: 20px;
}

/* 最后一列特殊样式 */
.key-management-table :deep(.table-body-cell:last-child) {
    padding-right: 20px;
}
</style>
