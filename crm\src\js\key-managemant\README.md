# Key Management 页面

这是一个基于 Figma 设计图开发的钥匙管理页面，实现了完整的搜索、筛选和表格展示功能。

## 功能特性

- ✅ 搜索功能：支持按物业地址、持有人、钥匙ID搜索
- ✅ 筛选功能：支持按状态、持有人、物业类型筛选
- ✅ 表格展示：显示物业信息、钥匙代码、持有人、状态、最后访问时间
- ✅ 状态标签：带颜色圆点的状态显示
- ✅ 响应式设计：适配不同屏幕尺寸
- ✅ 组件化设计：按最佳实践拆分组件

## 组件结构

```
crm/src/js/key-managemant/
├── index.vue                 # 主组件
├── components/
│   ├── SearchFilter.vue      # 搜索和筛选组件
│   ├── KeyTable.vue          # 表格组件
│   ├── StatusBadge.vue       # 状态标签组件
│   └── PropertyItem.vue      # 物业信息组件
└── README.md
```

## 组件说明

### 主组件 (index.vue)
- 负责数据管理和组件协调
- 处理搜索和筛选逻辑
- 包含模拟数据

### SearchFilter.vue
- 搜索输入框（带搜索图标）
- 三个筛选下拉框：状态、持有人、物业类型
- 支持点击外部关闭下拉框

### KeyTable.vue
- 使用 DataTable 组件展示数据
- 包含5列：物业地址、钥匙代码、持有人、状态、最后访问时间
- 响应式列宽设计

### StatusBadge.vue
- 显示带颜色圆点的状态标签
- 支持4种状态：Available、Checked Out、Archived、Lost/Damaged
- 每种状态有对应的颜色

### PropertyItem.vue
- 显示物业图片和地址信息
- 使用随机图片服务
- 包含物业ID显示

## 技术栈

- Vue 2.7
- DataTable 组件（来自 common 包）
- icon2017 字体图标系统
- Less 样式预处理器

## 访问方式

开发环境访问地址：
```
http://localhost:8080/admin/home/<USER>
```

## 设计图参考

基于 Figma 设计图开发：
https://www.figma.com/design/z7nqNYTiaHRsycSjAIP7MQ/Property-Detail?node-id=182-4006

## 状态配置

```javascript
const statusConfig = {
    'available': {
        text: 'Available',
        color: '#20C472'  // 绿色
    },
    'checked_out': {
        text: 'Checked Out', 
        color: '#5D51E2'  // 紫色
    },
    'archived': {
        text: 'Archived',
        color: '#C6C8D1'  // 灰色
    },
    'lost_damaged': {
        text: 'Lost/Damaged',
        color: '#F0454C'  // 红色
    }
};
```

## 图标使用

- 搜索图标：`icon-search_01`
- 下拉箭头：`icon-arrow_01_down`

所有图标都使用 `i` 标签 + `icon2017` 类名的方式实现。

## 注意事项

1. 图片使用随机图片服务 `picsum.photos`
2. 组件按照最佳规范进行拆分
3. 样式遵循现有项目规范
4. 不包含顶部导航栏和底部分页（按需求要求）
