<!-- header  deal with  -->
<template>
    <div class="header-wrap">
        <!-- TODO   It needs to judge whether to display according to the authority  -->
        <template v-if="whiteListShow">
            <div class="btn-box" v-if="currentMode !== 'editing'">
                <span class="custom-btn btn-item customize-btn" @click="updatePageState('editing')">
                    <i class="icon2017 icon-edit_01"></i>
                    {{ $t("btns.customizeText") }}</span
                >
            </div>
            <div class="btn-box" v-else>
                <span class="cancel-btn btn-item" @click.stop="cancelHandler">{{
                    $t("btns.cancelText")
                }}</span>
                <span class="sure-btn btn-item custom-btn" @click="updateHandler">{{
                    $t("btns.publishText")
                }}</span>
            </div>
        </template>

        <div
            ref="titleEditRef"
            v-if="titleState === 'normal'"
            class="title-item main title-normal"
            :class="{ edit: currentMode === 'editing' }"
            @click.stop="titleStateEdit"
        >
            <div class="label" :key="titleState + '_' + curTitle" v-text="curTitle"></div>
            <span
                v-if="titleEditIconShow"
                class="icon2017 icon-draw_06"
                @click.stop="titleStateEdit"
            ></span>
        </div>
        <!--  edit main title   Components to be extracted  -->
        <div
            ref="titleEditRef"
            :class="{ 'error-text': curTitleEmptyShow }"
            class="title-item main edit title-edit"
            v-else
        >
            <div
                contenteditable="true"
                ref="titleEditChildRef"
                class="title-input full-width no-border-input"
                @input="titleInputHandler($event, 'curTitle', 45)"
                @blur="titleBlurHandler"
                v-text="cacheTitle"
            ></div>

            <div class="icon-box main">
                <span class="icon2017 icon-cancel_bold" @click="titleEditCancel"></span>
                <span class="icon2017 icon-checked_bold" @click="titleEditSure"></span>
            </div>
        </div>
        <p v-if="curTitleEmptyShow" class="error-title main">
            {{ $t("main.tip1") }}
        </p>

        <SearchPanel
            v-show="currentMode !== 'editing'"
            v-bind="$attrs"
            v-on="$listeners"
        ></SearchPanel>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { popMgr } from "common";
import { basePermission } from "crm";
import headerV2 from "@/js/common-module/headerV2/index.js";
import SearchPanel from "../components/searchPanel";

export default {
    langModule: "marketPlace",
    props: {
        //  Three parties are not affected by the whitelist
        canEdit: {
            type: Boolean,
            default: false
        }
    },
    components: {
        SearchPanel
    },
    data() {
        const { customizeMP, LucidoTheme } = basePermission;
        // store data here
        return {
            titleState: "normal",
            subTitleState: "normal",
            curTitle: "",
            curSubTitle: "",
            curTitleEmptyShow: false,
            cacheTitle: "",
            cacheSubTitle: "",
            customizeMP,
            LucidoTheme
        };
    },
    computed: {
        ...mapState(["title", "subtitle", "currentMode"]),
        titleEditIconShow({ titleState, currentMode }) {
            return currentMode === "editing" && titleState === "normal";
        },
        whiteListShow({ canEdit, customizeMP }) {
            //  If whitelist is configured   or   tripartite canEdit  for true  then show the button
            return customizeMP || canEdit;
        }
    },
    watch: {
        title: {
            handler(val) {
                this.curTitle = val;
                this.cacheTitle = val;
            },
            immediate: true
        },
        subtitle: {
            handler(val) {
                this.curSubTitle = val;
                this.cacheSubTitle = val;
            },
            immediate: true
        }
    },
    methods: {
        ...mapMutations(["updateTitleList", "updateSubtitleList", "updateCurrentMode"]),

        ...mapActions(["updateMarketPlaceData", "initialization"]),

        updatePageState(val) {
            //  if only   button display   have editing rights
            this.updateCurrentMode(val);
            this.initialization({ isInit: false });
        },
        updateTitleState(attr, val) {
            this[attr] = val;
        },
        //  Input box processing
        titleInputHandler(e, attr, maxLength) {
            const textStr = e.target.innerText.replace(/\n\n/g, "\n");
            if (textStr.length > maxLength) {
                this[attr] = textStr.substr(0, maxLength);
                e.target.innerText = this[attr];
                e.target.blur();
                return;
            }
            if (textStr.length <= 0) {
                this[attr + "EmptyShow"] = true;
                e.target.focus();
            } else {
                this[attr + "EmptyShow"] = false;
            }
            this[attr] = textStr;
        },
        subtitleBlurHandler() {
            this.subTitleEditSure();
        },
        titleBlurHandler() {
            this.titleEditSure();
        },
        // Focus cursor to editable div end of content (el for DOM object )
        focusEnd(el) {
            el.focus();
            if ($.support.msie) {
                const range = document.selection.createRange();
                this.last = range;
                range.moveToElementText(el);
                range.select();
                document.selection.empty(); // uncheck
            } else {
                const range = document.createRange();
                range.selectNodeContents(el);
                range.collapse(false);
                // return a Selection object ， Represents the range of text selected by the user or the current position of the cursor
                const sel = window.getSelection();
                sel.removeAllRanges();
                sel.addRange(range);
            }
        },
        //normal ➡️ edit
        titleStateEdit() {
            //  only the current edit status will appear edit
            if (this.currentMode !== "editing") {
                return;
            }
            if (this.subTitleState === "edit" && this.curSubTitle.length === 0) {
                this.focusEnd(this.$refs.subTitleEditChildRef);
                return;
            }
            this.curTitleEmptyShow = false;
            this.titleState = "edit";
            this.subTitleState = "normal";
            this.$nextTick(() => {
                this.focusEnd(this.$refs.titleEditChildRef);
            });
        },

        // ✅
        titleEditSure() {
            if (this.curTitle.length === 0) {
                this.curTitleEmptyShow = true;
                this.focusEnd(this.$refs.titleEditChildRef);
                return;
            }
            // this.updateTitleList(this.curTitle);
            this.cacheTitle = this.curTitle;
            console.log(this.cacheTitle, this.curTitle);
            this.titleState = "normal";
        },
        //  Cancel
        titleEditCancel() {
            this.titleState = "normal";
            this.curTitleEmptyShow = false;
            this.$nextTick(() => {
                this.curTitle = this.cacheTitle;
            });
        },
        // ✅
        subTitleEditSure() {
            if (this.curSubTitle.length === 0) {
                this.focusEnd(this.$refs.subTitleEditChildRef);
                return;
            }
            this.cacheSubTitle = this.curSubTitle;
            this.subTitleState = "normal";
        },
        subTitleEditCancel() {
            this.subTitleState = "normal";
            this.$nextTick(() => {
                this.curSubTitle = this.cacheSubTitle;
            });
        },
        //  cancel operation   back to editing
        async cancelHandler() {
            //
            const { curTitle, curSubTitle, title, subtitle } = this;
            if (curTitle.length === 0) {
                this.curTitle = title;
                this.curTitleEmptyShow = false;
            }
            if (curSubTitle.length === 0) {
                this.curSubTitle = subtitle;
            }
            const { ok } = await popMgr.confirm({
                desc: this.$t("main.confirm1"),
                okText: this.$st("common", "yes"),
                cancelText: this.$st("common", "no"),
                maxWidth: "440px"
            });
            if (!ok) {
                return;
            }
            //
            this.curTitle = title;
            this.curSubTitle = subtitle;
            this.$emit("reset-current-mode");
            this.initialization({ isInit: false });
        },
        // update  operate
        async updateHandler() {
            const { curTitle, curSubTitle } = this;
            if (curTitle.length === 0 || curSubTitle.length === 0) {
                return;
            }
            const { ok } = await popMgr.confirm({
                desc: this.$t("main.confirm2"),
                okText: this.$st("common", "yes"),
                cancelText: this.$st("common", "cancel"),
                maxWidth: "440px"
            });
            if (!ok) {
                return;
            }
            this.updateTitleList(curTitle);
            this.updateSubtitleList(curSubTitle);
            await this.updateMarketPlaceData();
            this.$emit("reset-current-mode");
            this.updateTitleState("titleState", "normal");
            this.updateTitleState("subTitleState", "normal");

            //  only crm  Only called in the background
            if (this.currentMode === "normal") {
                await basePermission.initPermissionObj(); //  Initialize permissions
                let header = headerV2;
                header.init();
            }
            this.initialization({ isInit: true });
        }
    }
};
</script>
<style lang="less" scoped>
//@import url();  introduce public css kind
@import url("../../style/common.less");
* {
    box-sizing: border-box;
}
.header-wrap {
    width: 100%;
    padding: 0 240px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    position: relative;
    background: linear-gradient(104deg, #202437, #393e56 70%);
    :deep(.marketplace-search-panel) {
        position: absolute;
        width: 720px;
        height: 50px;
        left: 50%;
        transform: translateX(-50%);
        bottom: -25px;
        background: #ffffff;
        z-index: 2;
        .com-dropdown-text {
            line-height: 50px;
            font-weight: 700;
            font-size: 14px;
            color: #202437;
        }
    }
    &::before {
        content: " ";
        background: url(https://cdn.chime.me/image/fs/sitebuild/2023517/23/original_195e823b-5a2f-47f5-a236-c9cbc54a561e.png);
        width: 50%;
        height: 100%;
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        min-height: 140px;
        background-position: top 0 left 0;
        background-repeat: no-repeat;
        background-size: 600px;
        z-index: 0;
    }
    &::after {
        content: " ";
        background: url(https://cdn.chime.me/image/fs/sitebuild/2023517/23/original_975bc815-2a8f-4dda-895b-5dca39396e6b.png);
        width: 50%;
        height: 100%;
        display: block;
        position: absolute;
        right: 0;
        bottom: 0;
        min-height: 140px;
        background-position: bottom 0 right 0;
        background-repeat: no-repeat;
        background-size: 600px;
        z-index: 0;
    }
}
.btn-box {
    position: absolute;
    text-align: right;
    display: flex;
    top: 20px;
    right: 20px;
    z-index: 9;
}
.btn-item {
    font-size: 14px;
    text-align: center;
    border-radius: 15px;
    cursor: pointer;
    line-height: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    padding: 7px 15px;
    min-width: 100px;
    i {
        margin-right: 5px;
    }
}
.cancel-btn {
    border: 1px solid #ffffff;
    &:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    &:active {
        background: rgba(255, 255, 255, 0.3);
    }
}
.sure-btn {
    box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
    &:hover {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), #ffffff;
    }
    &:active {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #ffffff;
    }
}
.custom-btn {
    background: #ffffff;
    color: #515666;
}
.customize-btn {
    box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
    &:hover {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), #ffffff;
    }
    &:active {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #ffffff;
    }
}
.sure-btn {
    color: var(--primary-color);
}
.btn-item + .btn-item {
    margin-left: 10px;
}
// normal
.title-item {
    position: relative;
    padding: 0;
    z-index: 9;

    .label {
        text-align: center;
    }
    .title-input,
    .label {
        white-space: pre-line;
        word-break: break-word;
        border: 1px solid transparent;
        padding: 0px 10px;
    }
    .icon-draw_06 {
        margin-left: 10px;
        display: block;
        background: rgba(var(--primary-color-rgb), 0.1);
        box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
        width: 30px;
        height: 30px;
        border-radius: 50%;
        color: var(--primary-color);
        font-size: 13px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: -40px;
        &:hover {
            background: rgba(var(--primary-color-rgb), 0.2);
        }
        &:active {
            background: rgba(var(--primary-color-rgb), 0.3);
        }
    }
    &.edit {
        .label:hover {
            border: 1px solid var(--primary-color);
        }
    }
}
.title-item.title-normal {
    margin: 30px 0 52px;
    display: flex;
}

.title-item.main {
    font-weight: 700;
    font-size: 44px;
    line-height: 56px;
    .label {
        max-width: 650px;
    }
}

.title-item.subtitle {
    font-size: 14px;
    margin-top: 12px;
    margin-bottom: 35px;
    max-width: 1180px;
    .icon-edit_01 {
        font-size: 12px;
        top: 50%;
        transform: translateY(-50%);
    }
}

.error-text.title-item.subtitle {
    margin-bottom: 0;
}

////// edit /////

.title-item.main.edit.title-edit {
    .title-input {
        max-width: 620px;
        border: 1px solid var(--primary-color);
    }
}

.title-item.subtitle.edit.title-edit {
    width: 1180px;
}

.title-item.title-edit {
    margin: 30px 0 52px;
}
.subtitle.title-item.title-edit {
    display: flex;
    align-items: flex-start;
}

.title-input {
    width: 100%;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    caret-color: var(--primary-color-5);
    padding: 5px 40px;
    text-align: center;
    color: #ffffff;
    appearance: none;
    outline: none;
}

.icon-box {
    position: absolute;
    right: 5px;
    .icon2017 {
        cursor: pointer;
    }
}
.icon-box.main {
    top: 0;
    right: -80px;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon2017 {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #ffffff;
        box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
        font-size: 13px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .icon2017 + .icon2017 {
        margin-left: 10px;
    }
    .icon-cancel_bold {
        color: @error-color;
        box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
        &:hover {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), #ffffff;
        }
        &:active {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #ffffff;
        }
    }
    .icon-checked_bold {
        color: var(--primary-color);
        box-shadow: 0px 5px 10px rgba(32, 36, 55, 0.05);
        &:hover {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), #ffffff;
        }
        &:active {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #ffffff;
        }
    }
}
.icon-box.sub {
    font-size: 12px;
    bottom: 6px;
    .icon2017 + .icon2017 {
        margin-left: 5px;
    }
}
.title-item.title-edit.error-text {
    background: rgba(240, 69, 76, 0.1);
    border: 1px solid @error-color;
}

.error-title {
    position: relative;
    font-size: 14px;
    color: @error-color;
    margin-top: 5px;
    width: 1140px;
    text-align: left;
}
.main.error-title {
    width: 1130px;
}
.sub.error-title {
    width: 1180px;
    margin-bottom: 35px;
}
</style>
