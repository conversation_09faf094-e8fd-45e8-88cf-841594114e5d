<template>
    <div >
        <div>header</div>
        <div>counts</div>
        <div class="key-management-wrapper">
            <SearchFilter
                @search-change="handleSearchChange"
                @filter-change="handleFilterChange"
        />
        <KeyTable :data="filteredData" />
    </div>
</div>
</template>

<script>
import SearchFilter from './components/SearchFilter.vue';
import KeyTable from './components/KeyTable.vue';

export default {
    name: 'KeyManagement',
    components: {
        SearchFilter,
        KeyTable
    },
    data() {
        return {
            searchKeyword: '',
            filters: {
                status: 'all',
                holder: 'all',
                propertyType: 'all'
            },
            mockData: [
                {
                    keyId: 'Key001',
                    address: "100 Queen's Gate London, SW7 5AG",
                    keyCode: 'SEC001',
                    holder: '<PERSON>',
                    status: 'checked_out',
                    lastAccess: '1 Jul 2025'
                },
                {
                    keyId: 'Key002',
                    address: '101 Westbourne Grove London, W2 4UW',
                    keyCode: 'SEC002',
                    holder: '<PERSON>',
                    status: 'available',
                    lastAccess: '30 Jun 2025'
                },
                {
                    keyId: 'Key003',
                    address: '1002 Downing Street London, SW1A 2AB',
                    keyCode: 'SEC003',
                    holder: 'Bessie <PERSON>',
                    status: 'checked_out',
                    lastAccess: 'Never'
                },
                {
                    keyId: 'Key004',
                    address: '1234 Lambeth Road London, SE1 7JL',
                    keyCode: 'SEC004',
                    holder: 'Esther Howard',
                    status: 'archived',
                    lastAccess: '28 Jun 2025'
                },
                {
                    keyId: 'Key005',
                    address: '3 Berkeley Street London, W1J 8DL',
                    keyCode: 'SEC005',
                    holder: 'Cody Fisher',
                    status: 'lost_damaged',
                    lastAccess: '27 Jun 2025'
                },
                {
                    keyId: 'Key006',
                    address: '120A West Granton Road Edinburgh, EH5 1PF',
                    keyCode: 'SEC006',
                    holder: 'Dianne Russell',
                    status: 'available',
                    lastAccess: '26 Jun 2025'
                }
            ]
        };
    },
    computed: {
        filteredData() {
            let data = this.mockData;

            // 搜索过滤
            if (this.searchKeyword) {
                const keyword = this.searchKeyword.toLowerCase();
                data = data.filter(item =>
                    item.address.toLowerCase().includes(keyword) ||
                    item.holder.toLowerCase().includes(keyword) ||
                    item.keyId.toLowerCase().includes(keyword) ||
                    item.keyCode.toLowerCase().includes(keyword)
                );
            }

            // 状态过滤
            if (this.filters.status !== 'all') {
                data = data.filter(item => item.status === this.filters.status);
            }

            // 持有人过滤
            if (this.filters.holder !== 'all') {
                const holderMap = {
                    'ralph_edwards': 'Ralph Edwards',
                    'eleanor_pena': 'Eleanor Pena',
                    'bessie_cooper': 'Bessie Cooper',
                    'esther_howard': 'Esther Howard',
                    'cody_fisher': 'Cody Fisher',
                    'dianne_russell': 'Dianne Russell'
                };
                const holderName = holderMap[this.filters.holder];
                if (holderName) {
                    data = data.filter(item => item.holder === holderName);
                }
            }

            return data;
        }
    },
    methods: {
        handleSearchChange(keyword) {
            this.searchKeyword = keyword;
        },
        handleFilterChange(filters) {
            this.filters = { ...filters };
        }
    }
};
</script>

<style scoped>
.key-management-page {}
.key-management-wrapper {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 20px;
    background: #F6F7FB;
    min-height: 100vh;
}
</style>
