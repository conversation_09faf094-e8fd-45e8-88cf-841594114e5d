<template>
  <div class="timeline">
    <div class="tags-tab">
      <div class="tag active">All <img src="https://via.placeholder.com/150" alt="arrow down" /></div>
      <div class="tag"><img src="https://via.placeholder.com/150" alt="call icon" /> 3</div>
      <div class="tag"><img src="https://via.placeholder.com/150" alt="mail icon" /> 6</div>
      <div class="tag"><img src="https://via.placeholder.com/150" alt="message icon" /> 8</div>
      <div class="tag"><img src="https://via.placeholder.com/150" alt="note icon" /> 5</div>
    </div>
    <div class="list">
      <div class="list-item" v-for="(item, index) in timelineItems" :key="index">
        <div class="timeline-type" :style="{ backgroundColor: item.bgColor }">
          <img :src="item.icon" :alt="item.iconAlt" />
        </div>
        <div class="text">
          <div class="row">
            <p>{{ item.title }}</p>
            <div class="right-area">
              <div class="icon-area"><img src="https://via.placeholder.com/150" alt="edit icon" /></div>
              <div class="icon-area"><img src="https://via.placeholder.com/150" alt="delete icon" /></div>
            </div>
          </div>
          <p class="subtitle">{{ item.subtitle }}</p>
          <p class="date">{{ item.date }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Timeline',
  data() {
    return {
      timelineItems: [
        {
          bgColor: 'rgba(118, 141, 232, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'mail sent icon',
          title: '[Manual E-Mail] Jenny Wilson sent email to Brown',
          subtitle: 'Schedule a Property Showing',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(142, 223, 93, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'mail opened icon',
          title: '[Auto E-Mail] Marvin McKinney opened email',
          subtitle: 'Your Home Evaluation Report',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(118, 141, 232, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'mail received icon',
          title: '[Auto E-Mail] Robert Fox to Brown',
          subtitle: 'Hi Maggie, I saw your reviews on Lofty. My parents are in town this weekend, and we are looking at buying a house in the San Jose area.',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(107, 166, 255, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'chat sent icon',
          title: '[Auto Text] Bernard texted AI Assistant',
          subtitle: 'Hi Maggie, I saw your reviews on Lofty. My parents are in town this weekend, and we are looking at buying a house in the San Jose area. Are you free to chat this evening or this weekend ? They are here for the weekend, and so could even visit a few houses. Thanks for your time! Cecilia',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(107, 166, 255, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'chat received icon',
          title: '[Auto Text] Bernard texted AI Assistant',
          subtitle: 'Hi Maggie, I saw your reviews on Lofty. My parents are in town this weekend, and we are looking at buying a house in the San Jose area. Are you free to chat this evening or this weekend ? They are here for the weekend, and so could even visit a few houses. Thanks for your time! Cecilia',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(80, 227, 153, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'call out icon',
          title: 'Bernard called Brown (+1 3213211234) :Talked',
          subtitle: 'Hi Maggie, I saw your reviews on Lofty.',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
        {
          bgColor: 'rgba(80, 227, 153, 1)',
          icon: 'https://via.placeholder.com/150',
          iconAlt: 'call in icon',
          title: 'Bernard called Brown (+1 3213211234) :Talked',
          subtitle: 'My parents are in town this weekend, and we are looking at buying a house in the San Jose area.',
          date: 'May 15, 2025 at 03:44:44 AM',
        },
      ],
    };
  },
};
</script>

<style scoped>
.timeline {
  border: 1px solid #ebecf1;
  border-radius: 6px;
}
.tags-tab {
  display: flex;
  gap: 10px;
  padding: 20px;
  border-bottom: 1px solid #ebecf1;
}
.tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 15px;
  height: 30px;
  border-radius: 6px;
  font-size: 13px;
  background-color: #f6f7fb;
  color: #797e8b;
}
.tag.active {
  background-color: #5d51e2;
  color: #fff;
}
.list {
  padding: 20px;
}
.list-item {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
.timeline-type {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  width: 100%;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebecf1;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}
.row p {
  font-size: 15px;
  font-weight: 510;
  color: #515666;
}
.right-area {
  display: flex;
  gap: 10px;
}
.icon-area {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.subtitle {
  font-size: 14px;
  color: #5d51e2;
  margin-bottom: 6px;
}
.date {
  font-size: 12px;
  color: #a0a3af;
}
</style>